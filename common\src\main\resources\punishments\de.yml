# %check_name% - Name der Prüfung
# %description% - description of the check
# %vl% - Verstöße
# %verbose% - zusätzliche Informationen
# %player% - Spielername
# [alert] - spezieller Befehl zur Alarmierung
# [webhook] - spezieller Befehl für die Alarmierung an den Discord-Webhook
# [proxy] - spezieller Befehl zur Alarmierung anderer Server, die mit Ihrem Proxy verbunden sind (BungeeCord/Velocity)
Punishments:
  Simulation:
    # Nach wie vielen Sekunden soll ein Verstoß entfernt werden?
    remove-violations-after: 300
    # In diesem Abschnitt werden alle Prüfungen mit dem Namen gefunden,
    # Um eine Prüfung auszuschließen, die sonst übereinstimmen würde, setzen Sie ein Ausrufezeichen vor den Namen
    # Zum Beispiel, !BadPacketsN
    checks:
      - "Simulation"
      - "GroundSpoof"
      - "Timer"
      - "TimerLimit"
      - "NoFall"
    # Schwellenwert:Intervall Befehl
    #
    # Beispiel, um den Spieler mit der Meldung "Falsche Bewegung!" nach 100 Verstößen zu kicken, ohne Intervall
    # Befehle:
    # - "100:0 kick %player% incorrect movement!"
    # 0 bedeutet genau einmal ausführen
    # - "100:50 say %player% is cheating"
    # Ausführen, wenn der Benutzer Flagge 100 trifft, und danach jede 50. Flagge nach 100
    #
    commands:
      - "100:40 [alert]"
      - "100:40 [log]"
      - "100:100 [webhook]"
      - "100:100 [proxy]"
  Knockback:
    remove-violations-after: 300
    checks:
      - "Knockback"
      - "Explosion"
    commands:
      - "5:5 [alert]"
      - "5:5 [log]"
      - "20:20 [webhook]"
      - "20:20 [proxy]"
  Post:
    remove-violations-after: 300
    checks:
      - "Post"
    commands:
      - "20:20 [alert]"
      - "20:20 [log]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  BadPackets:
    remove-violations-after: 300
    checks:
      - "BadPackets"
      - "PacketOrder"
      - "Crash"
    commands:
      - "20:20 [alert]"
      - "20:20 [log]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  Reach:
    remove-violations-after: 300
    checks:
      - "Reach"
    commands:
      - "1:1 [alert]"
      - "1:1 [log]"
      - "1:1 [webhook]"
      - "1:1 [proxy]"
  Hitboxes:
    remove-violations-after: 300
    checks:
      - "Hitboxes"
    commands:
      - "5:3 [alert]"
      - "5:3 [log]"
      - "5:3 [webhook]"
      - "5:3 [proxy]"
  Misc:
    remove-violations-after: 300
    checks:
      - "Vehicle"
      - "NoSlow"
      - "Sprint"
      - "MultiActions"
      - "Place"
      - "Baritone"
      - "Break"
      - "TransactionOrder"
      - "Elytra"
      - "Chat"
      - "Exploit"
    commands:
      - "10:5 [alert]"
      - "10:5 [log]"
      - "20:10 [webhook]"
      - "20:10 [proxy]"
  Combat:
    remove-violations-after: 300
    checks:
      - "Interact"
      - "Killaura"
      - "Aim"
    commands:
      - "20:40 [alert]"
      - "20:40 [log]"
  # Ab 2.2.10 gibt es keine AutoClicker-Prüfungen mehr und dies ist ein Platzhalter. Grim wird in Zukunft AutoClicker-Prüfungen einbauen.
  Autoclicker:
    remove-violations-after: 300
    checks:
      - "Autoclicker"
    commands:
      - "20:40 [alert]"
      - "20:40 [log]"
