# Contributing to GrimAC

Thank you for your interest in contributing to GrimAC. This document outlines the guidelines for
making pull
requests to the project. *We're usually pretty lenient with pull requests, but this guide will help
make the process go more smoothly.*

### Acceptable Pull Requests

**Bug fixes** and **new features** are welcome. However, some things like heuristic-based checks
will **not** be accepted.

### Pull Request Guidelines

- Ensure any changes are compatible with
  the [supported environments](https://github.com/GrimAnticheat/Grim/wiki/Supported-environments).
- Create a new branch for your feature or fix when forking the repository.
- Write clear and descriptive commit messages.
- Reference related issues in your pull request description if applicable.
- Add code comments for complex logic or significant changes.
- Try to keep your code clean and avoid duplication.
- Thoroughly test your changes before submitting your pull request.

### Development Notes

- GrimAC is built using [Gradle](https://gradle.org/).
- Java 21 is currently required to build the project, but it is not required to run it.

### Questions & Support

- Join our [Discord](https://discord.grim.ac) if you have questions or need assistance.
- Refer to the [Wiki](https://github.com/GrimAnticheat/Grim/wiki) for project documentation.
