# GrimAC main configuration
# Source code: https://github.com/MWHunter/Grim
# Copyright 2025 DefineOutside and contributors, Licensed under GPLv3.
# Modified binaries, or plugins with copied grim code, must be private,
# or with full source code available to buyers at no additional cost.

alerts:
    # In aggiunta alla trasmissione degli avvisi ai giocatori, dovrebbero essere inviati anche alla console?
    print-to-console: true
    # Questo controlla se e come gli avvisi vengono condivisi tra i server collegati al tuo proxy.
    # Deve essere abilitato 'bungee-plugin-message-channel' nella configurazione di Velocity se Velocity è in uso.
    proxy:
        # Gli avvisi devono essere inviati ad altri server collegati al tuo proxy?
        send: false
        # Gli avvisi ricevuti da altri server devono essere annunciati a questo server?
        receive: false

verbose:
    # Mostra dettagli su console?
    print-to-console: false

check-for-updates: true

client-brand:
    # Ignora i seguenti client nella trasmissione del marchio agli operatori
    ignored-clients:
        - "^vanilla$"
        - "^fabric$"
        - "^lunarclient:v\\d+\\.\\d+\\.\\d+-\\d{4}$"
        - "^Feather Fabric$"
        - "^labymod$"
    # Grim will blacklist specific Forge versions which include built-in Reach hacks (Forge 1.18.2 to 1.19.3).
    # Setting this option to false will allow said clients to connect to the server. Disable this at your own risk.
    disconnect-blacklisted-forge-versions: true

spectators:
    # Nascondi tutti gli spettatori con il permesso grim.spectator indipendentemente dal fatto che stiano effettivamente spettando attivamente
    hide-regardless: false
    # Renderà gli spettatori nascosti solo in questi mondi, lasciare vuoto per consentire tutti i mondi
    allowed-worlds:
        - ""

max-transaction-time: 60

# Enable this to stop Grim cancelling pongs received from players.
# This may improve compatibility with other anticheats. It may cause issues with packet limiters.
# Do not enable this if you are not sure what you are doing.
disable-pong-cancelling: false

# Should the duplicate movement packet be cancelled?
# Mojang has fixed this issue in 1.21. This was their attempt to fix the "bucket desync". https://bugs.mojang.com/browse/MC-12363
# This setting only applies to 1.17-1.20.5 clients on 1.8 servers.
cancel-duplicate-packet: true

# Whether or not to ignore the rotation in duplicate packets
ignore-duplicate-packet-rotation: false

Simulation:
    # Riduce gradualmente l'avanzamento totale del giocatore quando è legittimo
    setback-decay-multiplier: 0.999
    # Limite per creare una violazione rispetto al movimento del giocatore
    threshold: 0.001
    # Soglia per arretrare immediatamente il giocatore dopo una violazione significativa
    immediate-setback-threshold: 0.1
    # Massimo vantaggio ammesso prima di arretrare il giocatore
    max-advantage: 1
    # Limite massimo di vantaggio accumulabile prima di arretrare il giocatore
    max-ceiling: 4
    # Soglia del livello di violazione per il setback
    # 1 per il comportamento precedente
    setback-violation-threshold: 1

Phase:
    # Livello di violazione per il glitch nel blocco
    setbackvl: 1
    # Decadimento per il glitch nel blocco
    decay: 0.005

AirLiquidPlace:
    # Livello di violazione per il piazzamento in aria o liquido
    cancelvl: 0

FabricatedPlace:
    # Livello di violazione per il piazzamento di un blocco fabbricato
    cancelvl: 5

FarPlace:
    # Livello di violazione per il piazzamento in un punto lontano
    cancelvl: 5

PositionPlace:
    # Livello di violazione per il piazzamento in una posizione specifica
    cancelvl: 5

RotationPlace:
    # Livello di violazione per il piazzamento con rotazione specifica
    cancelvl: 5

NoSlow:
    # Limite per rilevare l'uso scorretto dell'oggetto
    threshold: 0.001
    # Livello di violazione per l'uso scorretto dell'oggetto
    setbackvl: 5
    # Decadimento per l'uso scorretto dell'oggetto
    decay: 0.05

Knockback:
    # Riduce gradualmente l'avanzamento totale del giocatore quando è legittimo
    setback-decay-multiplier: 0.999
    # Limite per creare una violazione rispetto alla velocità del giocatore
    threshold: 0.001
    # Soglia per arretrare immediatamente il giocatore dopo una violazione significativa
    immediate-setback-threshold: 0.1
    # Massimo vantaggio ammesso prima di arretrare il giocatore
    max-advantage: 1
    # Limite massimo di vantaggio accumulabile prima di arretrare il giocatore
    max-ceiling: 4

Explosion:
    # Limite per rilevare l'esplosione
    threshold: 0.001
    # Livello di violazione per l'esplosione
    setbackvl: 3

TimerA:
    # Livello di violazione per il timer
    setbackvl: 10
    # Millisecondi accumulabili per il timer
    drift: 120

# This check limits abuse of the TimerA balance by preventing the player's movement falling too far behind realtime
TimerLimit:
    # Ping at which the check will start to limit timer balance, to prevent abuse.
    # Can cause some setbacks for legitimate players but only if they are over this ping threshold.
    # -1 to disable
    ping-abuse-limit-threshold: 1000

NegativeTimer:
    # Millisecondi persi prima di rilevare il timer negativo
    drift: 1200

VehicleTimer:
    # Livello di violazione per il timer dei veicoli
    setbackvl: 10

PacketOrderI:
    # Abilitare se i giocatori vengono segnalati per l'uso di mod di animazione 1.7
    exempt-placing-while-digging: false

Reach:
    # Limite per rilevare un attacco oltre la distanza massima
    threshold: 0.0005
    # Annulla gli attacchi impossibili
    block-impossible-hits: true
    # Abilita l'invio di pacchetti aggiuntivi per il rilevamento degli attacchi
    enable-post-packet: false

exploit:
    # Abilita il salto in sprint con l'elytra
    allow-sprint-jumping-when-using-elytra: true
    # Abilita la costruzione su ghost blocks
    allow-building-on-ghostblocks: true
    # Distanza per controllare i ghost blocks
    distance-to-check-if-ghostblocks: 2

# Enable logging plugins who have injected into netty on join to debug compatibility issues
debug-pipeline-on-join: false

# Enables experimental checks
experimental-checks: false

reset-item-usage-on-item-update: true
reset-item-usage-on-attack: true
reset-item-usage-on-slot-change: true

# Grim sometimes cancels illegal packets such as with timer, after X packets in a second cancelled, when should
# we simply kick the player? This is required as some packet limiters don't count packets cancelled by grim.
packet-spam-threshold: 100
# Abilita questa opzione per stampare uno stacktrace quando un giocatore viene espulso a causa del packet-spam-threshold
debug-packet-cancel: false

# Grim può imporre che un giocatore uscito dallo stato di volo non abbia più di X ms di ping
# Questo perché Grim al momento non controlla i giocatori in volo
# Per disattivare, usa -1
max-ping-out-of-flying: 1000

# Ping massimo quando il boost del fuoco d’artificio viene rimosso dal giocatore.
# Impedisce ai giocatori con alta latenza di usare all’infinito un solo boost con fuochi d’artificio ed elytra.
max-ping-firework-boost: 1000

history:
    enabled: true
    # Quante voci devono essere mostrate per pagina con /grim history <player>
    entries-per-page: 15
    # Quale nome server deve essere inserito per il comando history? Utile se usi lo stesso database per più server
    server-name: Prison
    database:
        # Usa SQLITE per l’archiviazione locale, MYSQL se hai un database MySQL esterno. Aggiornato solo al riavvio del server
        type: SQLITE
        # Dettagli di connessione MySQL
        host: localhost
        port: 3306
        database: grim
        username: root
        password: ""

config-version: 9
