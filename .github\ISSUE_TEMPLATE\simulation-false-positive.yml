name: Simulation false positive
description: Report a simulation (movement-related) false positive here
labels: ["false positive", "simulation"]
body:
  - type: textarea
    attributes:
      label: Describe the false positive and how to replicate it
      description: |
        Video is always useful.
        Use /grim verbose and /grim debug to get the simulation debug information.
        A minimally reproducible description is best.
      placeholder: |
        Step 1. Do this
        Step 2. Then do this
        Step 3. etc...
    validations:
      required: true

  - type: input
    attributes:
      label: Grim Log
      description: |
        Provide the generated /grim log link.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Server version
      description: |
        Run `/version` on your server to check. Provide both the server type (paper, airplane, etc) & the minecraft version.
    validations:
      required: true

  - type: input
    attributes:
      label: Client version
      description: |
        Provide the version of your Minecraft client. You must be able to reproduce the false positive using the vanilla client. 
        Lunar Client, TLauncher etc. will result in your issue being closed.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Plugins
      description: |
        List all plugins or mods running on your server. To list plugins on bukkit, run `/plugins`.
        If your server is behind a proxy such as Bungeecord, you should list the proxy's plugins as well.
    validations:
        required: true
