# GrimAC main configuration
# Source code: https://github.com/MWHunter/Grim
# Copyright 2025 DefineOutside and contributors, Licensed under GPLv3.
# Modified binaries, or plugins with copied grim code, must be private,
# or with full source code available to buyers at no additional cost.

alerts:
    # In addition to broadcasting alerts to players, should they also be sent to the console?
    print-to-console: true
    # This controls whether/how alerts are shared between servers connected to your proxy.
    # You must have 'bungee-plugin-message-channel' enabled in your Velocity's configuration if Velocity is in use.
    proxy:
        # Should alerts be sent to other servers connected to your proxy?
        send: false
        # Should the alerts received from other servers be announced to this server?
        receive: false

verbose:
    print-to-console: false

check-for-updates: true

client-brand:
    # This means it won't broadcast their brand to operators if the brand matches the following regexes
    ignored-clients:
        - "^vanilla$"
        - "^fabric$"
        - "^lunarclient:v\\d+\\.\\d+\\.\\d+-\\d{4}$"
        - "^Feather Fabric$"
        - "^labymod$"
    # Grim will blacklist specific Forge versions which include built-in Reach hacks (Forge 1.18.2 to 1.19.3).
    # Setting this option to false will allow said clients to connect to the server. Disable this at your own risk.
    disconnect-blacklisted-forge-versions: true

spectators:
    # Hide all spectators with the grim.spectator permission regardless if they are actually actively spectating
    hide-regardless: false
    # Will make spectators hidden only in these worlds, keep blank to allow all worlds
    allowed-worlds:
        - ""

# How long should players have until we kick them for timing out? Default = 60 seconds
max-transaction-time: 60

# Enable this to stop Grim cancelling pongs received from players.
# This may improve compatibility with other anticheats. It may cause issues with packet limiters.
# Do not enable this if you are not sure what you are doing.
disable-pong-cancelling: false

# Should the duplicate movement packet be cancelled?
# Mojang has fixed this issue in 1.21. This was their attempt to fix the "bucket desync". https://bugs.mojang.com/browse/MC-12363
# This setting only applies to 1.17-1.20.5 clients on 1.8 servers.
cancel-duplicate-packet: true

# Whether or not to ignore the rotation in duplicate packets
ignore-duplicate-packet-rotation: false

Simulation:
    # How much should we multiply total advantage by when the player is legit
    # This is what the default config looks like (x axis = seconds, y axis = 1/1000 block): https://www.desmos.com/calculator/d4ufgxrxer
    setback-decay-multiplier: 0.999
    # How large of an offset from the player's movement should we create a violation for?
    # Measured in blocks from the possible movement
    # We account for Optifine by switching trig tables but dropping this to 0.001 will reduce FastMath
    # flagging the anticheat if this compensation doesn't work...
    threshold: 0.001
    # How large of a violation in a tick before the player gets immediately setback?
    # -1 to disable
    immediate-setback-threshold: 0.1
    # How large of an advantage over all ticks before we start to setback?
    # -1 to disable
    max-advantage: 1
    # After 50 seconds with default config, the player will go from 4 blocks -> 1 block of advantage
    # This is to stop the player from gathering too many violations and never being able to clear them all
    # Default advantage ceiling (x axis = seconds, y axis = 1/1000 block): https://www.desmos.com/calculator/4lovswdarj
    max-ceiling: 4
    # Violation level threshold for setback
    # 1 for old behavior
    setback-violation-threshold: 1

# Checks to see if a player entered a block during a movement
Phase:
    setbackvl: 1 # Glitching into blocks can allow wall climbing, plus this check is relatively stable
    decay: 0.005

AirLiquidPlace:
    cancelvl: 0

FabricatedPlace:
    cancelvl: 5

FarPlace:
    cancelvl: 5

PositionPlace:
    cancelvl: 5

RotationPlace:
    cancelvl: 5

# Prediction-based no slow check
# Grim accounts for buggy netcode here... hard to false even when spamming right click and offhand button
# Much more stable than other anticheats, but please report any falses... I have fixed a ton of netcode issues here.
NoSlow:
    # How much of an offset is "cheating"
    # By default this is lower than other offs
    # Flags by 0.03-0.2 consistently with NoSlow on
    threshold: 0.001
    # Setback fast on the first item to remove any advantage NoSlow gives
    setbackvl: 5
    # Decay's when the player uses an item AND is slowed by it
    decay: 0.05

Knockback:
    # How much should we multiply total advantage by when the player is legit
    setback-decay-multiplier: 0.999
    # How large of an offset from the player's velocity should we create a violation for?
    # Measured in blocks from the possible velocity
    threshold: 0.001
    # How large of a violation in a tick before the player gets immediately setback?
    # -1 to disable
    immediate-setback-threshold: 0.1
    # How large of an advantage over all ticks before we start to setback?
    # -1 to disable
    max-advantage: 1
    # This is to stop the player from gathering too many violations and never being able to clear them all
    max-ceiling: 4

Explosion:
    threshold: 0.001
    setbackvl: 3

TimerA:
    setbackvl: 10
    # Milliseconds that the player can accumulate for later use when they fall behind
    # Could potentially allow 1.8 fast use/fast heal/fast bow bypasses if set too high, 120 ms seems like a good balance
    drift: 120

# This check limits abuse of the TimerA balance by preventing the player's movement falling too far behind realtime
TimerLimit:
    # Ping at which the check will start to limit timer balance, to prevent abuse.
    # Can cause some setbacks for legitimate players but only if they are over this ping threshold.
    # -1 to disable
    ping-abuse-limit-threshold: 1000

NegativeTimer:
    # Number of milliseconds lost while moving before we should start flagging
    drift: 1200

# Same check method as TimerA, but for vehicles
VehicleTimer:
    # Target 1.005 timer
    setbackvl: 10

PacketOrderI:
    # enable if players are getting flagged for using 1.7 animations mods
    exempt-placing-while-digging: false

Reach:
    # How much should we expand hitboxes by? 0.0005 should detect 3.0005+ reach
    #
    # There is 0.03 forced expansion with 1.9-1.18.1 (not 1.18.2), or some client/server combinations due to
    # protocol changes and limitations. This check is most powerful with 1.7/1.8 clients on 1.8 servers.
    threshold: 0.0005
    # Should we cancel hits that we know are impossible?
    # 3.00-3.03 hits may go through but still be flagged, due to packet order limitations
    block-impossible-hits: true
    # This will send an additional packet at the end of every tick to increase the likelihood of catching cheats
    # This injects into server's connection list to send a final packet just before the server flushes
    # Enabling this will increase bandwidth usage for all players
    # This will not decrease overall server performance
    # Enabling this will catch more cheaters.
    # Leaving this disabled will still catch cheaters and will not cause false positives
    # Unless you are a 1.8 PvP focused server, this additional packet is not recommended
    enable-post-packet: false

exploit:
    # You can gain high speeds when sprint jumping with an elytra, this prevents the exploit when set to false
    # Mojang screwed up netcode by making elytra start client sided and elytra end server sided
    # Elytras take 0.99 horizontal friction, so constantly adding 0.2 horizontal speeds results in very high speeds.
    allow-sprint-jumping-when-using-elytra: true
    # This option mitigates the player's placement on ghostblocks by resynchronizing the player when it happens
    allow-building-on-ghostblocks: true
    # This setting, influenced by the boolean above defines the distance to check for ghost blocks
    # Its valid range is limited from 2 to 4
    distance-to-check-if-ghostblocks: 2

# Enable logging plugins who have injected into netty on join to debug compatibility issues
debug-pipeline-on-join: false

# Enables experimental checks
experimental-checks: false

reset-item-usage-on-item-update: true
reset-item-usage-on-attack: true
reset-item-usage-on-slot-change: true

# Grim sometimes cancels illegal packets such as with timer, after X packets in a second cancelled, when should
# we simply kick the player? This is required as some packet limiters don't count packets cancelled by grim.
packet-spam-threshold: 100
# Enable this to print a stacktrace when a player is kicked due to packet-spam-threshold
debug-packet-cancel: false

# Grim is able to enforce that a player set out of flying state cannot have more than X milliseconds of ping
# This is due to Grim not currently checking flying players
# To disable, use -1
max-ping-out-of-flying: 1000

# Maximum ping when a firework boost is removed from the player.
# This prevents high latency players from being able to use 1 firework boost with an elytra forever.
max-ping-firework-boost: 1000

history:
    enabled: true
    # How many entries should be shown for each page with /grim history <player>
    entries-per-page: 15
    # What should the inserted server name be for the history command? This is useful if you use the same database for multiple servers
    server-name: Prison
    database:
        # Use SQLITE for local storage, use MYSQL if you have an external MySQL database. This is only updated on server restart
        type: SQLITE
        # MySQL connection details
        host: localhost
        port: 3306
        database: grim
        username: root
        password: ""

config-version: 9
