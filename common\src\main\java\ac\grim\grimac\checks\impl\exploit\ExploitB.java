package ac.grim.grimac.checks.impl.exploit;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.payload.PayloadBookEdit;
import com.github.retrooper.packetevents.event.PacketReceiveEvent;
import com.github.retrooper.packetevents.protocol.component.ComponentTypes;
import com.github.retrooper.packetevents.protocol.item.ItemStack;
import com.github.retrooper.packetevents.protocol.item.type.ItemType;
import com.github.retrooper.packetevents.protocol.item.type.ItemTypes;
import com.github.retrooper.packetevents.protocol.nbt.NBT;
import com.github.retrooper.packetevents.protocol.nbt.NBTCompound;
import com.github.retrooper.packetevents.protocol.nbt.NBTList;
import com.github.retrooper.packetevents.protocol.nbt.NBTString;
import com.github.retrooper.packetevents.protocol.packettype.PacketType;
import com.github.retrooper.packetevents.protocol.player.ClientVersion;
import com.github.retrooper.packetevents.protocol.player.InteractionHand;
import com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientEditBook;
import com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPlayerBlockPlacement;
import com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPluginMessage;
import com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientUseItem;
import com.google.gson.JsonSyntaxException;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.io.StringReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@CheckData(name = "ExploitB", description = "Invalid book edit")
public class ExploitB extends Check implements PacketCheck {
    private ItemStack oldBook;
    private InteractionHand hand;

    public ExploitB(GrimPlayer player) {
        super(player);
    }

    @Override
    public void onPacketReceive(PacketReceiveEvent event) {
        if (player.getClientVersion().isOlderThanOrEquals(ClientVersion.V_1_8)
                ? event.getPacketType() == PacketType.Play.Client.PLAYER_BLOCK_PLACEMENT
                && new WrapperPlayClientPlayerBlockPlacement(event).getFaceId() == 255
                : event.getPacketType() == PacketType.Play.Client.USE_ITEM) {
            hand = player.getClientVersion().isOlderThanOrEquals(ClientVersion.V_1_8)
                    ? InteractionHand.MAIN_HAND : new WrapperPlayClientUseItem(event).getHand();
            oldBook = player.getInventory().getItemInHand(hand).copy();
        }

        if (event.getPacketType() == PacketType.Play.Client.EDIT_BOOK) {
            checkEditBook(new WrapperPlayClientEditBook(event), event);
            oldBook = null;
        }

        // 1.13-
        if (event.getPacketType() == PacketType.Play.Client.PLUGIN_MESSAGE && player.getClientVersion().isOlderThan(ClientVersion.V_1_13)) {
            WrapperPlayClientPluginMessage wrapper = new WrapperPlayClientPluginMessage(event);

            boolean signing;
            switch (wrapper.getChannelName()) {
                case "MC|BSign" -> signing = true;
                case "MC|BEdit" -> signing = false;
                default -> {
                    return;
                }
            }

            ItemStack newBook = PayloadBookEdit.CODEC.read(wrapper.getData()).itemStack();
            checkItemStack(newBook, signing, event);
            oldBook = null;
        }
    }

    private void checkEditBook(WrapperPlayClientEditBook wrapper, PacketReceiveEvent event) {
        if (checkEditingBook(event)) return;

        int expectedSlot = hand == InteractionHand.OFF_HAND ? 40 : player.packetStateData.lastSlotSelected;
        if (wrapper.getSlot() != expectedSlot) {
            if (flagAndAlert("expectedSlot=" + expectedSlot + ", slot=" + wrapper.getSlot()) && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return;
        }

        if (wrapper.getSigning() == null) { // 1.17+
            if (wrapper.getTitle() != null && checkTitle(wrapper.getTitle(), event)) return;

            List<String> pages = wrapper.getPages();
            if (!pages.isEmpty() && checkEmptyLastPage(pages.get(pages.size() - 1), event)) return;
            if (checkTooManyPages(pages.size(), event)) return;
            for (String page : pages) {
                if (checkPage(page, event)) return;
            }
        } else { // 1.13 - 1.16.5
            checkItemStack(wrapper.getItemStack(), wrapper.getSigning(), event);
        }
    }

    private void checkItemStack(ItemStack newBook, boolean signing, PacketReceiveEvent event) {
        if (checkEditingBook(event)) return;

        ItemType expectedType = signing && player.getClientVersion().isOlderThanOrEquals(ClientVersion.V_1_8)
                ? ItemTypes.WRITTEN_BOOK : ItemTypes.WRITABLE_BOOK;
        if (!newBook.is(expectedType)) {
            if (flagAndAlert("expectedType=" + expectedType.getName().getKey() + ", type=" + newBook.getType().getName().getKey()) && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return;
        }

        if (newBook.getAmount() != oldBook.getAmount()) {
            if (flagAndAlert("expectedAmount=" + oldBook.getAmount() + ", amount=" + newBook.getAmount()) && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return;
        }

        if (newBook.getLegacyData() != oldBook.getLegacyData()) {
            if (flagAndAlert("expectedMeta=" + oldBook.getLegacyData() + ", meta=" + newBook.getLegacyData()) && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return;
        }

        NBTCompound newBookNBT = newBook.getNBT();
        if (newBookNBT == null) {
            if (flagAndAlert("no nbt") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return;
        }

        NBTList<@NotNull NBTString> newPagesNBT = newBookNBT.getStringListTagOrNull("pages");
        if (newPagesNBT == null) {
            if (flagAndAlert("no pages") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return;
        }

        if (checkTooManyPages(newPagesNBT.size(), event)) return;

        if (signing) {
            String title = newBookNBT.getStringTagValueOrNull("title");
            if (title == null) {
                if (flagAndAlert("no title") && shouldModifyPackets()) {
                    event.setCancelled(true);
                    player.onPacketCancel();
                }
                return;
            }

            if (checkTitle(title, event)) return;

            String author = newBookNBT.getStringTagValueOrNull("author");
            if (!player.getName().equals(author)) {
                if (flagAndAlert("expectedAuthor=" + player.getName() + ", author=" + author) && shouldModifyPackets()) {
                    event.setCancelled(true);
                    player.onPacketCancel();
                }
                return;
            }
        }

        String last = null;
        for (NBTString string : newPagesNBT.getTags()) {
            if (player.getClientVersion().isOlderThanOrEquals(ClientVersion.V_1_8) && signing) {
                try {
                    JsonReader reader = new JsonReader(new StringReader(string.getValue()));
                    if (reader.peek() != JsonToken.BEGIN_OBJECT) throw new JsonSyntaxException("");
                    reader.beginObject();
                    if (reader.peek() != JsonToken.NAME || !reader.nextName().equals("text")
                            || reader.peek() != JsonToken.STRING) throw new JsonSyntaxException("");
                    last = reader.nextString();
                    if (reader.peek() != JsonToken.END_OBJECT) throw new JsonSyntaxException("");
                } catch (JsonSyntaxException e) {
                    if (flagAndAlert("invalid page") && shouldModifyPackets()) {
                        event.setCancelled(true);
                        player.onPacketCancel();
                    }
                    return;
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            } else {
                last = string.getValue();
            }

            if (checkPage(last, event)) return;
        }

        if (last != null && checkEmptyLastPage(last, event)) {
            return;
        }

        Set<Map.Entry<String, NBT>> newNBT = newBookNBT.getTags().entrySet();
        HashMap<String, NBT> oldNBT = oldBook.getNBT() == null ? new HashMap<>() : new HashMap<>(oldBook.getNBT().getTags());
        oldNBT.remove("pages");
        oldNBT.remove("title");
        oldNBT.remove("author");
        for (var entry : newNBT) {
            if (!entry.getKey().equals("pages") && !entry.getKey().equals("title") && !entry.getKey().equals("author")) {
                NBT nbt = oldNBT.remove(entry.getKey());
                if (nbt == null || !nbt.equals(entry.getValue())) {
                    if (flagAndAlert("modified nbt") && shouldModifyPackets()) {
                        event.setCancelled(true);
                        player.onPacketCancel();
                    }
                    return;
                }
            }
        }

        if (!oldNBT.isEmpty()) {
            if (flagAndAlert("modified nbt") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
        }
    }

    private boolean checkTitle(@NotNull String title, PacketReceiveEvent event) {
        if (title.length() > (player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_16) ? 15 : 16)) {
            if (flagAndAlert("title too long") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return true;
        } else if (title.isEmpty() || !title.trim().equals(title) || containsInvalidCharacters(title)) {
            if (flagAndAlert("invalid title '" + title + "'") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return true;
        }

        return false;
    }

    private boolean checkEditingBook(PacketReceiveEvent event) {
        if (oldBook == null || !oldBook.is(ItemTypes.WRITABLE_BOOK)) {
            if (flagAndAlert("not editing book") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return true;
        }
        return false;
    }

    private boolean checkTooManyPages(int pages, PacketReceiveEvent event) {
        NBTList<@NotNull NBTString> oldPagesNBT;
        int oldPages = oldBook.getVersion().isNewerThanOrEquals(ClientVersion.V_1_20_5)
                ? oldBook.getComponent(ComponentTypes.WRITABLE_BOOK_CONTENT).map(content -> content.getPages().size()).orElse(0)
                : oldBook.getNBT() != null && (oldPagesNBT = oldBook.getNBT().getStringListTagOrNull("pages")) != null
                ? oldPagesNBT.size() : 0;

        if (pages > oldPages && pages > (player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_14) ? 100 : 50)) {
            if (flagAndAlert("too many pages") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return true;
        }

        return false;
    }

    private boolean checkPage(String page, PacketReceiveEvent event) {
        if (page.length() > 256) {
            if (flagAndAlert("page too long") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return true;
        }

        if (containsInvalidCharacters(page)) {
            if (flagAndAlert("invalid page characters") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return true;
        }

        return false;
    }

    private boolean checkEmptyLastPage(String page, PacketReceiveEvent event) {
        if (page.isEmpty()) {
            if (flagAndAlert("empty last page") && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
            return true;
        }

        return false;
    }

    private static boolean containsInvalidCharacters(@NotNull String string) {
        for (int i = 0; i < string.length(); ++i) {
            char character = string.charAt(i);
            if (character == 167 || character < 32 || character == 127) {
                return true;
            }
        }

        return false;
    }
}
