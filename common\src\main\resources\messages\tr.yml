# \u00BB eşittir » (çift >>), ANSI ve UTF-8 bunu farklı yorumlar. bu nedenle ? bile görebilirsiniz
prefix: "&bGrim &8\u00BB"

alerts-enabled: "%prefix% &fUyarılar aktif edildi."
alerts-disabled: "%prefix% &fUyarılar deaktif edildi."
verbose-enabled: "%prefix% &fVerbose enabled"
verbose-disabled: "%prefix% &fVerbose disabled"
brands-enabled: "%prefix% &fBrands enabled"
brands-disabled: "%prefix% &fBrands disabled"
client-brand-format: "%prefix% &f%player% adlı oyuncu %brand% kullanarak giriş yaptı."
console-specify-target: "%prefix% &cKonsol olarak bir hedef belirtmelisiniz!"
reloading: "%prefix% &7Reloading config..."
reloaded: "%prefix% &fConfig has been reloaded."
reload-failed: "%prefix% &cFailed to reload config."
player-not-found: "%prefix% &cOyuncu bulunamadı veya aktif değil!"
player-not-this-server: "%prefix% &cBu oyuncu sunucuda değil!"
spectate-return: "<click:run_command:/grim stopspectating><hover:show_text:\"/grim stopspectating\">\n%prefix% &fBir önceki konuma dönmek için buraya tıklayın.\n</hover></click>"
cannot-spectate-return: "%prefix% &cBunu yalnızca bir oyuncuyu izledikten sonra yapabilirsiniz."
cannot-run-on-self: "%prefix% &cBu komutu kendi üzerinizde kullanamazsınız!"
upload-log: "%prefix% &fDebug şu adrese yüklendi: %url%"
upload-log-start: "%prefix% &fLog yükleniyor... lütfen bekleyin."
upload-log-not-found: "%prefix% &cLog bulunamadı."
upload-log-upload-failure: "%prefix% &cLog yüklenirken bir problem oluştu, lütfen konsolu kontrol edin."
disconnect:
    timeout: "<lang:disconnect.timeout>"
    closed: "<lang:disconnect.closed>"
    error: "<red>An error occurred whilst processing packets. Please contact the administrators."
    blacklisted-forge: "<red>Your forge version is blacklisted due to inbuilt reach hacks.<newline><gold>Versions affected: 1.18.2-1.19.3<newline><newline><red>Please see https://github.com/MinecraftForge/MinecraftForge/issues/9309."
run-as-player: "%prefix% &cBu komut yalnızca oyuncular tarafından kullanılabilir!"
run-as-player-or-console: "%prefix% &cBu komut yalnızca oyuncular veya konsol tarafından kullanılabilir!"

# Geçerli placeholderlar:
# %prefix%
# %player%
# %check_name%
# %description%
# %experimental%
# %vl% - ihlal uyarıları
# %verbose% - detaylı olarak verilen tüm uyarılar, checkler tüm bilgileri vermez
alerts-format: "%prefix% &b%player% &fadlı oyuncuda &f(x&c%vl%&f) &fadet &b%check_name%%experimental% &ftespit edildi: &7%verbose%"
alerts-format-proxy: "%prefix% &f[&cproxy&f] &b%player% &fadlı oyuncuda &f(x&c%vl%&f) &fadet &b%check_name%%experimental% &ftespit edildi: &7%verbose%"
experimental-symbol: "*"

profile:
    - "&7======================"
    - "%prefix% &f%player% &badlı oyuncunun profili:"
    - "&bGecikme: &f%ping%"
    - "&bVersiyon: &f%version%"
    - "&bKullanılan Client: &f%brand%"
    - "&bDikey Hassasiyet: &f%h_sensitivity%%"
    - "&bYatay Hassasiyet: &f%v_sensitivity%%"
    - "&bFastMath Ayarı: &f%fast_math%"
    - "&7======================"
help:
    - "&7======================"
    - "/grim alerts &f- &7Uyarıları aktif/deaktif eder"
    - "/grim brands &f- &7Toggle brands"
    - "/grim profile (oyuncu) &f- &7Oyuncu profilini gösterir"
    - "/grim help &f- &7Bu yardım mesajını gösterir"
    - "/grim debug <player> &f- &7Geliştirici Debug'ını verir"
    - "/grim perf &f- &7Geliştirici Performansını verir"
    - "/grim reload &f- &7Configi yeniden yükler"
    - "/grim spectate (oyuncu) &f- &7Bir oyuncuyu izlemenizi sağlar"
    - "/grim verbose &f- &7Arabelleğe almadan tüm uyarıları görmenizi sağlar"
    - "/grim log (0-255) &f- &7Uyarılar için bir debug logu çıktısı verir"
    - "/grim history <player> [sayfa] &f- &7Oyuncunun önceki uyarılarını gösterir"
    - "&7======================"

grim-history-load-failure: "%prefix% &cGeçmiş altsistemi yüklenemedi! Hatalar için sunucu konsolunu kontrol edin."
grim-history-disabled: "%prefix% &cGeçmiş alt sistemi devre dışı!"
# Valid placeholders: %prefix% %player% %page% %maxPages%
grim-history-header: "%prefix% &bKayıtlar gösteriliyor: &f%player% (&f%page%&b/&f%maxPages%&f)"
# Valid placeholders: %prefix% %grim_version% %client_brand% %client_version% %server_version% %check% %vl% %verbose% %timeago% %server%
grim-history-entry: "%prefix% &8[&f%server%&8] &bBaşarısız &f%check% (x&c%vl%&f) &7%verbose% (&b%timeago% önce&7)"
