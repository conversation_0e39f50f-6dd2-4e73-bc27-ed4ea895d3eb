# » è » (doppia >>), ANSI e UTF-8 lo interpretano in modo diverso... si può anche vedere ? A causa di ciò
prefix: "&bGrim &8\u00BB"

alerts-enabled: "%prefix% &fAlerts abilitati"
alerts-disabled: "%prefix% &fAlerts disabilitati"
verbose-enabled: "%prefix% &fVerbose enabled"
verbose-disabled: "%prefix% &fVerbose disabled"
brands-enabled: "%prefix% &fBrands enabled"
brands-disabled: "%prefix% &fBrands disabled"
client-brand-format: "%prefix% &f%player% si è connesso usando %brand%"
console-specify-target: "%prefix% &cDevi specificare un destinatario come console!"
reloading: "%prefix% &7Reloading config..."
reloaded: "%prefix% &fConfig has been reloaded."
reload-failed: "%prefix% &cFailed to reload config."
player-not-found: "%prefix% &cIl giocatore è esente o offline!"
player-not-this-server: "%prefix% &cIl giocatore non è in questo server!"
spectate-return: "<click:run_command:/grim stopspectating><hover:show_text:\"/grim stopspectating\">\n%prefix% &fClicca qui per tornare alla posizione precedente\n</hover></click>"
cannot-spectate-return: "%prefix% &cPuoi farlo solo dopo aver osservato un giocatore"
cannot-run-on-self: "%prefix% &cNon puoi usare questo comando su te stesso!"
upload-log: "%prefix% &fDebug caricato su: %url%"
upload-log-start: "%prefix% &fCaricamento del registro... attendere prego"
upload-log-not-found: "%prefix% &cImpossibile trovare quel registro"
upload-log-upload-failure: "%prefix% &cQualcosa è andato storto durante il caricamento di questo registro, vedi la console per ulteriori informazioni"
disconnect:
    timeout: "<lang:disconnect.timeout>"
    closed: "<lang:disconnect.closed>"
    error: "<red>An error occurred whilst processing packets. Please contact the administrators."
    blacklisted-forge: "<red>Your forge version is blacklisted due to inbuilt reach hacks.<newline><gold>Versions affected: 1.18.2-1.19.3<newline><newline><red>Please see https://github.com/MinecraftForge/MinecraftForge/issues/9309."
run-as-player: "%prefix% &cQuesto comando può essere utilizzato solo dai giocatori!"
run-as-player-or-console: "%prefix% &cQuesto comando può essere utilizzato solo dai giocatori o dalla console!"

alerts-format: "%prefix% &f%player% &bfallito &f%check_name% &f(x&c%vl%&f) &7%verbose%"
alerts-format-proxy: "%prefix% &f[&cproxy&f] &f%player% &bfallito &f%check_name% &f(x&c%vl%&f) &7%verbose%"
experimental-symbol: "*"

profile:
    - "&7======================"
    - "%prefix% &bProfilo per &f%player%"
    - "&bPing: &f%ping%"
    - "&bVersione: &f%version%"
    - "&bBrand del Client: &f%brand%"
    - "&bSensibilità Orizzontale: &f%h_sensitivity%%"
    - "&bSensibilità Verticale: &f%v_sensitivity%%"
    - "&bFastMath Abilitato: &f%fast_math%"
    - "&7======================"
help:
    - "&7======================"
    - "/grim alerts &f- &7Attiva/disattiva gli alerts"
    - "/grim brands &f- &7Toggle brands"
    - "/grim profile <giocatore> &f- &7Visualizza le informazioni del giocatore"
    - "/grim help &f- &7Visualizza questo messaggio di aiuto"
    - "/grim debug <giocatore> &f- &7Output di previsione dello sviluppatore"
    - "/grim perf &f- &7Ms/predizione dello sviluppatore"
    - "/grim reload &f- &7Ricarica la configurazione"
    - "/grim spectate <giocatore> &f- &7Osserva un giocatore"
    - "/grim verbose &f- &7Mostra ogni segnalazione a te, senza buffer"
    - "/grim log [0-255] &f- &7Carica un registro di debug per le segnalazioni di previsione"
    - "/grim history <player> [pagina] &f- &7Mostra gli avvisi precedenti del giocatore"
    - "&7======================"

grim-history-load-failure: "%prefix% &cImpossibile caricare il sottosistema della cronologia! Controlla la console del server per errori."
grim-history-disabled: "%prefix% &cIl sottosistema cronologia è disabilitato!"
# Valid placeholders: %prefix% %player% %page% %maxPages%
grim-history-header: "%prefix% &bMostrando log per &f%player% (&f%page%&b/&f%maxPages%&f)"
# Valid placeholders: %prefix% %grim_version% %client_brand% %client_version% %server_version% %check% %vl% %verbose% %timeago% %server%
grim-history-entry: "%prefix% &8[&f%server%&8] &bFallito &f%check% (x&c%vl%&f) &7%verbose% (&b%timeago% fa&7)"
