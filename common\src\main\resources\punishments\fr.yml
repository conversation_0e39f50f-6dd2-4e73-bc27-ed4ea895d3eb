# %check_name% - Nom de la vérification
# %description% - description of the check
# %vl% - Violations
# %verbose% - Informations supplémentaires
# %player% - Nom du joueur
# [alert] - Commande spéciale pour alerter
# [webhook] - Commande spéciale pour alerter via un Webhook Discord
# [proxy] - Commande spéciale pour alerter les autres serveurs connectés à votre proxy (BungeeCord/Velocity)
Punishments:
  Simulation:
    # Au bout de combien de secondes une violation doit-elle être supprimée ?
    remove-violations-after: 300
    # Cette section correspondra à toutes les vérifications portant le nom indiqué,
    # Pour exclure une vérification qui serait sinon correspondante, placez un point d'exclamation devant le nom.
    # Par exemple, !BadPacketsN
    checks:
      - "Simulation"
      - "GroundSpoof"
      - "Timer"
      - "TimerLimit"
      - "NoFall"
    # Limite : Intervale entre chaque Commande
    #
    # Par exemple, pour exclure le joueur avec le message "Mouvement incorrect !" après 100 violations, sans intervale
    # commands:
    # - "100:0 kick %player% Mouvement incorrect !"
    # 0 pour exécuter qu'une seule fois
    # - "100:50 say %player% est en train de tricher !"
    # Exécuter lorsque l'utilisateur atteint 100 violations, puis toutes les 50 violations suivantes après les 100 violations.
    #
    commands:
      - "100:40 [alert]"
      - "100:40 [log]"
      - "100:100 [webhook]"
      - "100:100 [proxy]"
  Knockback:
    remove-violations-after: 300
    checks:
      - "Knockback"
      - "Explosion"
    commands:
      - "5:5 [alert]"
      - "5:5 [log]"
      - "20:20 [webhook]"
      - "20:20 [proxy]"
  Post:
    remove-violations-after: 300
    checks:
      - "Post"
    commands:
      - "20:20 [alert]"
      - "20:20 [log]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  BadPackets:
    remove-violations-after: 300
    checks:
      - "BadPackets"
      - "PacketOrder"
      - "Crash"
    commands:
      - "20:20 [alert]"
      - "20:20 [log]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  Reach:
    remove-violations-after: 300
    checks:
      - "Reach"
    commands:
      - "1:1 [alert]"
      - "1:1 [log]"
      - "1:1 [webhook]"
      - "1:1 [proxy]"
  Hitboxes:
    remove-violations-after: 300
    checks:
      - "Hitboxes"
    commands:
      - "5:3 [alert]"
      - "5:3 [log]"
      - "5:3 [webhook]"
      - "5:3 [proxy]"
  Misc:
    remove-violations-after: 300
    checks:
      - "Vehicle"
      - "NoSlow"
      - "Sprint"
      - "MultiActions"
      - "Place"
      - "Baritone"
      - "Break"
      - "TransactionOrder"
      - "Elytra"
      - "Chat"
      - "Exploit"
    commands:
      - "10:5 [alert]"
      - "10:5 [log]"
      - "20:10 [webhook]"
      - "20:10 [proxy]"
  Combat:
    remove-violations-after: 300
    checks:
      - "Interact"
      - "Killaura"
      - "Aim"
    commands:
      - "20:40 [alert]"
      - "20:40 [log]"
  # A partir de la version 2.2.10, il n'y a plus de vérifications pour AutoClicker et c'est un placeholder. Grim inclura des vérifications AutoClicker dans le futur.
  Autoclicker:
    remove-violations-after: 300
    checks:
      - "Autoclicker"
    commands:
      - "20:40 [alert]"
      - "20:40 [log]"
