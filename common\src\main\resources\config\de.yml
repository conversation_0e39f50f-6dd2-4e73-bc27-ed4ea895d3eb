# GrimAC Hauptkonfiguration
# Quellcode: https://github.com/MWHunter/Grim
# Copyright 2025 DefineOutside und Mitwirkende, lizenziert unter GPLv3.
# Modifizierte Binärdateien, oder Plugins mit kopiertem Grim-Code, müssen privat sein,
# oder mit vollständigem Quellcode für Käufer ohne zusätzliche Kosten verfügbar sein.

alerts:
    # Sollen die Warnungen nicht nur an die Spieler, sondern auch an die Konsole gesendet werden?
    print-to-console: true
    # Dies steuert, ob/wie Warnungen zwischen Servern, die mit Ihrem Proxy verbunden sind, geteilt werden.
    # Sie müssen 'bungee-plugin-message-channel' in Ihrer Velocity-Konfiguration aktiviert haben, wenn Velocity verwendet wird.
    proxy:
        # Sollen Warnungen an andere Server gesendet werden, die mit Ihrem Proxy verbunden sind?
        send: false
        # Sollen die von anderen Servern empfangenen Alarme diesem Server mitgeteilt werden?
        receive: false

verbose:
    print-to-console: false

check-for-updates: true

client-brand:
    # Das bedeutet, dass die Marke nicht an die Betreiber weitergegeben wird, wenn die Marke mit den folgenden Regexes übereinstimmt
    ignored-clients:
        - "^vanilla$"
        - "^fabric$"
        - "^lunarclient:v\\d+\\.\\d+\\.\\d+-\\d{4}$"
        - "^Feather Fabric$"
        - "^labymod$"
    # Grim will blacklist specific Forge versions which include built-in Reach hacks (Forge 1.18.2 to 1.19.3).
    # Setting this option to false will allow said clients to connect to the server. Disable this at your own risk.
    disconnect-blacklisted-forge-versions: true

spectators:
    # Alle Zuschauer mit der Berechtigung grim.spectator ausblenden, unabhängig davon, ob sie tatsächlich aktiv zuschauen
    hide-regardless: false
    # Die Zuschauer werden nur in diesen Welten versteckt, um alle Welten zuzulassen, bleiben sie leer.
    allowed-worlds:
        - ""

# Wie lange sollen Spieler Zeit haben, bis wir sie wegen Zeitüberschreitung rauswerfen? Standard = 60 Sekunden
max-transaction-time: 60

# Enable this to stop Grim cancelling pongs received from players.
# This may improve compatibility with other anticheats. It may cause issues with packet limiters.
# Do not enable this if you are not sure what you are doing.
disable-pong-cancelling: false

# Should the duplicate movement packet be cancelled?
# Mojang has fixed this issue in 1.21. This was their attempt to fix the "bucket desync". https://bugs.mojang.com/browse/MC-12363
# This setting only applies to 1.17-1.20.5 clients on 1.8 servers.
cancel-duplicate-packet: true

# Whether or not to ignore the rotation in duplicate packets
ignore-duplicate-packet-rotation: false

Simulation:
    # Mit wie viel soll der Gesamtvorteil multipliziert werden, wenn der Spieler legitim ist.
    # So sieht die Standardkonfiguration aus (x-Achse = Sekunden, y-Achse = 1/1000 Block): https://www.desmos.com/calculator/d4ufgxrxer
    setback-decay-multiplier: 0.999
    # Wie groß sollte der Abstand zur Bewegung des Spielers sein, um eine Verletzung zu erzeugen?
    # Gemessen in Blöcken von der möglichen Bewegung.
    # Wir berücksichtigen Optifine, indem wir die Triggertabellen wechseln, aber wenn wir dies auf 0,001 senken, wird FastMath reduziert.
    # Markierung des Anticheats, wenn diese Kompensation nicht funktioniert...
    threshold: 0.001
    # Wie groß ist der Verstoß in einem Tick, bevor der Spieler sofort zurückgesetzt wird?
    # -1 zum Deaktivieren
    immediate-setback-threshold: 0.1
    # Wie groß ist der Vorsprung gegenüber allen Ticks, bevor wir einen Rückschlag erleiden?
    # -1 zum Deaktivieren
    max-advantage: 1
    # Nach 50 Sekunden mit der Standardkonfiguration geht der Spieler von 4 Blöcken -> 1 Block Vorteil
    # Dies soll verhindern, dass der Spieler zu viele Verstöße sammelt und nie in der Lage ist, sie alle zu beseitigen.
    # Standard-Vorteilsgrenze (x-Achse = Sekunden, y-Achse = 1/1000 Block): https://www.desmos.com/calculator/4lovswdarj
    max-ceiling: 4
    # Schwellenwert für die Verletzungsstufe für den Rückschlag
    # 1 für das alte Verhalten
    setback-violation-threshold: 1

# Überprüft, ob ein Spieler während einer Bewegung einen Block betreten hat.
Phase:
    setbackvl: 1 # Glitching in Blöcken kann das Klettern an der Wand ermöglichen, außerdem ist dieser Check relativ stabil.
    decay: 0.005

AirLiquidPlace:
    cancelvl: 0

FabricatedPlace:
    cancelvl: 5

FarPlace:
    cancelvl: 5

PositionPlace:
    cancelvl: 5

RotationPlace:
    cancelvl: 5

# Prediction-based no slow check
# Grim ist verantwortlich für den fehlerhaften Netcode hier... schwer zu falsen, selbst wenn man Rechtsklick und Offhand-Taste spammt.
# Viel stabiler als andere Anticheats, aber bitte melde alle Falses... Ich habe hier eine Menge Netcode-Probleme behoben.
NoSlow:
    # Wie viel von einem Offset ist "schummeln"
    # Standardmäßig ist dieser Wert niedriger als andere Offs.
    # Flags um 0,03-0,2 konsequent mit NoSlow on
    threshold: 0.001
    # Schnell auf das erste Element zurücksetzen, um jeden Vorteil von NoSlow zu beseitigen
    setbackvl: 5
    # Verfall ist, wenn der Spieler einen Gegenstand benutzt UND dadurch verlangsamt wird
    decay: 0.05

Knockback:
    # Mit wie viel soll der Gesamtvorteil multipliziert werden, wenn der Spieler legitim ist.
    setback-decay-multiplier: 0.999
    # Wie groß sollte der Abstand zur Bewegung des Spielers sein, um eine Verletzung zu erzeugen?
    # Gemessen in Blöcken von der möglichen Bewegung.
    threshold: 0.001
    # Wie groß ist der Verstoß in einem Tick, bevor der Spieler sofort zurückgesetzt wird?
    # -1 zum Deaktivieren
    immediate-setback-threshold: 0.1
    # Wie groß ist der Vorsprung gegenüber allen Ticks, bevor wir einen Rückschlag erleiden?
    # -1 zum Deaktivieren
    max-advantage: 1
    # Dies soll verhindern, dass der Spieler zu viele Verstöße sammelt und nie in der Lage ist, sie alle zu beseitigen.
    max-ceiling: 4

Explosion:
    threshold: 0.001
    setbackvl: 3

TimerA:
    setbackvl: 10
    # Millisekunden, die der Spieler akkumulieren kann, um sie später zu nutzen, wenn er zurückfällt.
    # Könnte möglicherweise 1,8 schnelle Nutzung/schnelle Heilung/schnelle Bogenumgehungen ermöglichen, wenn zu hoch eingestellt, 120 ms scheint eine gute Balance zu sein
    drift: 120

# This check limits abuse of the TimerA balance by preventing the player's movement falling too far behind realtime
TimerLimit:
    # Ping at which the check will start to limit timer balance, to prevent abuse.
    # Can cause some setbacks for legitimate players but only if they are over this ping threshold.
    # -1 to disable
    ping-abuse-limit-threshold: 1000

NegativeTimer:
    # Anzahl der Millisekunden, die während der Bewegung verloren gehen, bevor mit der Markierung begonnen werden sollte.
    drift: 1200

# Gleiche Prüfmethode wie TimerA, aber für Fahrzeuge
VehicleTimer:
    # Ziel 1,005 Timer
    setbackvl: 10

VehicleC:
    setbackvl: 25

PacketOrderI:
    # Aktivieren, wenn Spieler markiert werden, weil sie 1.7-Animationsmods verwenden
    exempt-placing-while-digging: false

Reach:
    # Um wie viel sollen wir die Hitboxen erweitern? 0,0005 sollte 3,0005+ Reichweite erkennen
    #
    # Es gibt eine 0,03 erzwungene Erweiterung mit 1.9-1.18.1 (nicht 1.18.2), oder einigen Client/Server-Kombinationen aufgrund von
    # Protokolländerungen und -beschränkungen. Diese Prüfung ist am stärksten mit 1.7/1.8 Clients auf 1.8 Servern.
    threshold: 0.0005
    # Sollten wir Treffer annullieren, von denen wir wissen, dass sie unmöglich sind?
    # 3.00-3.03-Treffer können aufgrund von Beschränkungen der Paketreihenfolge durchgehen, aber dennoch gekennzeichnet werden.
    block-impossible-hits: true
    # Dies sendet ein zusätzliches Paket am Ende jedes Ticks, um die Wahrscheinlichkeit zu erhöhen, dass Betrüger erwischt werden.
    # Dies injiziert in die Verbindungsliste des Servers, um ein letztes Paket zu senden, kurz bevor der Server geleert wird.
    # Aktivieren dieser Funktion erhöht die Bandbreitennutzung für alle Spieler.
    # Dies wird die Gesamtleistung des Servers nicht verringern.
    # Aktivieren Sie diese Option, um mehr Cheater zu erwischen.
    # Bleibt diese Funktion deaktiviert, werden immer noch Cheater erwischt und es kommt nicht zu Fehlalarmen.
    # Sofern es sich nicht um einen 1.8 PvP-Server handelt, wird dieses zusätzliche Paket nicht empfohlen.
    enable-post-packet: false

exploit:
    allow-sprint-jumping-when-using-elytra: true
    # Diese Option verhindert das Platzieren von Geisterblöcken auf weitere Geisterblöcke, indem der Spieler neu synchronisiert wird.
    allow-building-on-ghostblocks: true
    # Diese Option, abhängig von der oben genannten Einstellung, definiert wie weit man nach Geisterblöcken suchen soll
    # Ihr gültiger Bereich ist von 2 bis 4 begrenzt
    distance-to-check-if-ghostblocks: 2

# Aktivieren Sie die Protokollierung von Plugins, die in netty on join injiziert wurden, um Kompatibilitätsprobleme zu beheben.
debug-pipeline-on-join: false

# Aktiviert experimentelle Prüfungen
experimental-checks: false

reset-item-usage-on-item-update: true
reset-item-usage-on-attack: true
reset-item-usage-on-slot-change: true

# Grim bricht manchmal illegale Pakete ab, z.B. mit Timer, nachdem X Pakete in einer Sekunde abgebrochen wurden, wann sollte
# wir den Spieler einfach kicken? Dies ist erforderlich, da einige Paketbegrenzer die von Grim abgebrochenen Pakete nicht zählen.
packet-spam-threshold: 100
# Aktiviere dies, um einen Stacktrace auszugeben, wenn ein Spieler wegen packet-spam-threshold gekickt wird
debug-packet-cancel: false

# Grim kann erzwingen, dass ein Spieler, der aus dem Flugzustand gesetzt wurde, nicht mehr als X ms Ping haben darf
# Dies liegt daran, dass Grim derzeit fliegende Spieler nicht überprüft
# Zum Deaktivieren -1 verwenden
max-ping-out-of-flying: 1000

# Maximale Latenz, wenn ein Feuerwerks‑Boost vom Spieler entfernt wird.
# Dies verhindert, dass Spieler mit hoher Latenz einen einzigen Feuerwerks‑Boost mit Elytra unbegrenzt nutzen können.
max-ping-firework-boost: 1000

history:
    enabled: true
    # Wie viele Einträge sollen pro Seite mit /grim history <player> angezeigt werden
    entries-per-page: 15
    # Welcher Servername soll für den History‑Befehl eingetragen werden? Nützlich, wenn dieselbe Datenbank für mehrere Server verwendet wird
    server-name: Prison
    database:
        # SQLITE für lokale Speicherung, MYSQL für eine externe MySQL‑Datenbank. Wird nur nach Serverneustart aktualisiert
        type: SQLITE
        # MySQL‑Verbindungsdetails
        host: localhost
        port: 3306
        database: grim
        username: root
        password: ""

config-version: 9
