# La configuración principal de GrimAC
# Código fuente: https://github.com/MWHunter/Grim
# Copyright 2025 DefineOutside y contribuyentes, licenciado bajo GPLv3.
# Binarios modificados o plugins con código de Grim deben ser privados o con
# el código fuente disponible para todos los compradores sin costo adicional.

alerts:
    # ¿Además de mandar las alertas a los jugadores, deberíamos mandarlas también a la consola?
    print-to-console: true
    # This controls whether/how alerts are shared between servers connected to your proxy.
    # You must have 'bungee-plugin-message-channel' enabled in your Velocity's configuration if Velocity is in use.
    proxy:
        # Should alerts be sent to other servers connected to your proxy?
        send: false
        # Should the alerts received from other servers be announced to this server?
        receive: false

verbose:
    print-to-console: false

check-for-updates: true

client-brand:
    # Esto quiere decir que no se va a anunciar la marca del cliente si esta cumple alguno de los siguientes regexes:
    ignored-clients:
        - "^vanilla$"
        - "^fabric$"
        - "^lunarclient:v\\d+\\.\\d+\\.\\d+-\\d{4}$"
        - "^Feather Fabric$"
        - "^labymod$"
    # Grim will blacklist specific Forge versions which include built-in Reach hacks (Forge 1.18.2 to 1.19.3).
    # Setting this option to false will allow said clients to connect to the server. Disable this at your own risk.
    disconnect-blacklisted-forge-versions: true

spectators:
    # Ocultar todos los espectadores con el permiso grim.spectator sin importar si realmente están espectando
    hide-regardless: false
    # ¿En qué mundos deberíamos ocultar los espectadores? Si está en blanco, se ocultaran en todos los mundos.
    allowed-worlds:
        - ""

# ¿Cuánto tiempo de espera debería pasar antes de expulsar un jugador por "tiempo de espera agotado"?
# Por defecto, este valor se encuentra en 60 segundos.
max-transaction-time: 60

# Enable this to stop Grim cancelling pongs received from players.
# This may improve compatibility with other anticheats. It may cause issues with packet limiters.
# Do not enable this if you are not sure what you are doing.
disable-pong-cancelling: false

# Should the duplicate movement packet be cancelled?
# Mojang has fixed this issue in 1.21. This was their attempt to fix the "bucket desync". https://bugs.mojang.com/browse/MC-12363
# This setting only applies to 1.17-1.20.5 clients on 1.8 servers.
cancel-duplicate-packet: true

# Whether or not to ignore the rotation in duplicate packets
ignore-duplicate-packet-rotation: false

Simulation:
    # Por cuanto deberíamos multiplicar la ventaja total cuando el jugador es legítimo
    # Asi es como se ve la configuración por defecto (eje x = segundos, eje y = bloque 1/1000): https://www.desmos.com/calculator/d4ufgxrxer
    setback-decay-multiplier: 0.999
    # ¿Cuánta debería ser la compensacion del movimiento del jugador como para que creemos una violación?
    # Medido en bloques del posible movimiento
    # Tomamos en cuenta a OptiFine cambiando las tablas trigonometricas, pero bajar esto a 0.001 reducirá
    # que FastMath marque el anti-cheat si esta compensación no funciona...
    threshold: 0.001
    # ¿Qué tan grande debería ser una violación en un tick antes de que el jugador se retroceda inmediatamente?
    # -1 para deshabilitar
    immediate-setback-threshold: 0.1
    # ¿Qué tan grande debería ser una ventaja sobre todos los ticks para que empecemos a retroceder?
    # -1 para deshabilitar
    max-advantage: 1
    # Después de 50 segundos con la configuración predeterminada, el jugador ira de 4 bloques -> 1 bloque de ventaja
    # Esto es para prevenir que el jugador obtenga muchas violaciones y no pueda ser capaz de borrarlas
    # Tope de ventaja por defecto (eje x = segundos, eje y = bloque 1/1000): https://www.desmos.com/calculator/4lovswdarj
    max-ceiling: 4
    # Umbral del nivel de violación para el retroceso
    # 1 para el comportamiento antiguo
    setback-violation-threshold: 1

# Comprobaciones para ver si un jugador entro a un bloque durante un movimiento
Phase:
    setbackvl: 1 # Entrar a un bloque mediante bugs puede permitir subir paredes, además esta comprobación es relativamente estable.
    decay: 0.005

AirLiquidPlace:
    cancelvl: 0

FabricatedPlace:
    cancelvl: 5

FarPlace:
    cancelvl: 5

PositionPlace:
    cancelvl: 5

RotationPlace:
    cancelvl: 5

# Comprobación de NoSlow basado en predicciones
# Grim compensa por código de red con errores aquí... difícil de sacar un falso positivo incluso haciendo spam del
# clic derecho y la mano secundaria.
# Mucho mas estable que otros anti-cheats, pero por favor reportar todo falso positivo... he arreglado muchos
# problemas del código de red aquí.
NoSlow:
    # ¿Cuánta debería ser la compensacion como para que se considere trampa?
    # Por defecto esto es más bajo que otras compensaciones
    # Marca por 0.03-0.2 consistentemente con NoSlow activado
    threshold: 0.001
    # Retroceder rápidamente en el primer item para eliminar toda ventaja que NoSlow brinde
    setbackvl: 5
    # Decadencia cuando un jugador usa un item Y se ralentiza por el
    decay: 0.05

Knockback:
    # Por cuanto deberíamos multiplicar la ventaja total cuando el jugador es legítimo
    setback-decay-multiplier: 0.999
    # ¿Cuánta debería ser la compensacion del movimiento del jugador como para que creemos una violación?
    # Medido en bloques del posible movimiento
    threshold: 0.001
    # ¿Qué tan grande debería ser una violación en un tick antes de que el jugador se retroceda inmediatamente?
    # -1 para deshabilitar
    immediate-setback-threshold: 0.1
    # ¿Qué tan grande debería ser una ventaja sobre todos los ticks para que empecemos a retroceder?
    # -1 para deshabilitar
    max-advantage: 1
    # Esto es para prevenir que el jugador obtenga muchas violaciones y no pueda ser capaz de borrarlas
    max-ceiling: 4


Explosion:
    threshold: 0.001
    setbackvl: 3

TimerA:
    setbackvl: 10
    # Milisegundos que el jugador puede acumular para ser usados mas tarde cuando se quedan por detrás
    # Podría llegar a permitir pasos por alto de fast use/fast heal/fast bow en 1.8 si esta muy alto, 120 ms
    # parece ser un balance bastante decente.
    drift: 120

# This check limits abuse of the TimerA balance by preventing the player's movement falling too far behind realtime
TimerLimit:
    # Ping at which the check will start to limit timer balance, to prevent abuse.
    # Can cause some setbacks for legitimate players but only if they are over this ping threshold.
    # -1 to disable
    ping-abuse-limit-threshold: 1000

NegativeTimer:
    # Number of milliseconds lost while moving before we should start flagging
    drift: 1200

# La misma comprobación que TimerA, pero para vehiculos
VehicleTimer:
    # Target 1.005 timer
    setbackvl: 10

PacketOrderI:
    # Habilitar si los jugadores están siendo marcados por usar mods de animación de la 1.7
    exempt-placing-while-digging: false

Reach:
    # Cuanto deberíamos expandir las hit-boxes? 0.0005 debería detectar un reach de 3.0005+
    #
    # Hay una expansion forzada de 0.03 con 1.9-1.18.1 (1.18.2 no) y algunas combinaciones de clientes/servidores
    # debido a cambios en el protocolo y limitaciones. Esta comprobación es más poderosa en clientes 1.7 y 1.8
    # conectados a un servidor 1.8
    threshold: 0.0005
    # ¿Deberíamos cancelar golpes que sabemos que son imposibles?
    # Golpes de 3.00-3.03 puede que aun asi pasen, pero se marcaran debido a limitaciones del orden de paquetes.
    block-impossible-hits: true
    # Esto mandará un paquete adicional al final de cada tick para aumentar las chances de encontrar trampas
    # Esto se inyecta en la lista de conexiones del servidor para mandar un paquete final justo antes de que el servidor haga "flush".
    # Habilitar esto causará que el uso de banda ancha de todos los jugadores aumente
    # Esto no disminuirá el rendimiento del servidor.
    # Habilitar esto ayudará a encontrar más jugadores haciendo trampa.
    # Dejar esto deshabilitado también encontrara a jugadores haciendo trampa y no causara falsos positivos.
    # Excepto que seas un servidor 1.8 enfocado en el PvP, este paquete adicional no es recomendado.
    enable-post-packet: false

exploit:
    allow-sprint-jumping-when-using-elytra: true
    # Esta opción mitiga la colocación del jugador en bloques fantasma resincronizando al jugador cuando ocurre.
    allow-building-on-ghostblocks: true
    distance-to-check-if-ghostblocks: 2

# Habilitar el registro de plugins que se han inyectado a netty para intentar resolver problemas de compatibilidad.
debug-pipeline-on-join: false

# Habilitar comprobaciones experimentales
experimental-checks: false

reset-item-usage-on-item-update: true
reset-item-usage-on-attack: true
reset-item-usage-on-slot-change: true

# Grim a veces cancela paquetes ilegal como los de Timer. Después de X paquetes en un solo segundo cancelados,
# cuando deberíamos simplemente expulsar al jugador? Esto es obligatorio ya que algunos limitadores de paquetes
# no cuentan los paquetes cancelados por Grim.
packet-spam-threshold: 100
# Activa esto para imprimir un stacktrace cuando un jugador sea expulsado por packet-spam-threshold
debug-packet-cancel: false

# Grim puede hacer cumplir que un jugador que ha salido del estado de vuelo no tenga más de X ms de ping
# Esto se debe a que Grim actualmente no comprueba a los jugadores que están volando
# Para desactivar, usa -1
max-ping-out-of-flying: 1000

# Ping máximo cuando se elimina el impulso de fuegos artificiales del jugador.
# Esto evita que los jugadores con alta latencia puedan usar un solo impulso con fuegos artificiales y una elytra para siempre.
max-ping-firework-boost: 1000

history:
    enabled: true
    # Cuántas entradas se mostrarán por página con /grim history <player>
    entries-per-page: 15
    # ¿Qué nombre de servidor debe insertarse para el comando history? Útil si compartes la misma base de datos entre varios servidores
    server-name: Prison
    database:
        # Usa SQLITE para almacenamiento local o MYSQL si tienes una base de datos MySQL externa. Solo se actualiza al reiniciar el servidor
        type: SQLITE
        # Detalles de conexión MySQL
        host: localhost
        port: 3306
        database: grim
        username: root
        password: ""

config-version: 9
