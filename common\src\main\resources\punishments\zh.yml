# %check_name% - 检查名称
# %description% - 检查的介绍
# %vl% - 违规
# %verbose% - 额外信息
# %player% - 玩家名字
# [alert] - 警告的特殊命令
# [webhook] - 警告discord webhook 的特殊命令
# [proxy] - 用于使用(BungeeCord/Velocity)服务器时的警报
Punishments:
  Simulation:
    # 多少秒后重置VL
    remove-violations-after: 300
    # 这个部分将会匹配检查的名字
    # 要关闭掉一个检查就在检查前面加感叹号
    # 例如, !BadPacketsN
    checks:
      - "Simulation"
      - "GroundSpoof"
      - "Timer"
      - "TimerLimit"
      - "NoFall"
    # 最大vl:下一次执行需要的vl
    #
    # 这是个例子, 当到达100VL时为了以 "incorrect movement!" 为理由踢出玩家
    # commands:
    # - "100:0 kick %player% incorrect movement!"
    # 后面的数如果使用0就代表只执行一次
    # - "100:50 say %player% is cheating"
    # 在100vl后执行say %player% is cheating,之后的每50vl都执行一次say %player% is cheating
    #
    commands:
      - "100:40 [alert]"
      - "100:40 [log]"
      - "100:100 [webhook]"
      - "100:100 [proxy]"
  Knockback:
    remove-violations-after: 300
    checks:
      - "Knockback"
      - "Explosion"
    commands:
      - "5:5 [alert]"
      - "5:5 [log]"
      - "20:20 [webhook]"
      - "20:20 [proxy]"
  Post:
    remove-violations-after: 300
    checks:
      - "Post"
    commands:
      - "20:20 [alert]"
      - "20:20 [log]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  BadPackets:
    remove-violations-after: 300
    checks:
      - "BadPackets"
      - "PacketOrder"
      - "Crash"
    commands:
      - "20:20 [alert]"
      - "20:20 [log]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  Reach:
    remove-violations-after: 300
    checks:
      - "Reach"
    commands:
      - "1:1 [alert]"
      - "1:1 [log]"
      - "1:1 [webhook]"
      - "1:1 [proxy]"
  Hitboxes:
    remove-violations-after: 300
    checks:
      - "Hitboxes"
    commands:
      - "5:3 [alert]"
      - "5:3 [log]"
      - "5:3 [webhook]"
      - "5:3 [proxy]"
  Misc:
    remove-violations-after: 300
    checks:
      - "Vehicle"
      - "NoSlow"
      - "Sprint"
      - "MultiActions"
      - "Place"
      - "Baritone"
      - "Break"
      - "TransactionOrder"
      - "Elytra"
      - "Chat"
      - "Exploit"
    commands:
      - "10:5 [alert]"
      - "10:5 [log]"
      - "20:10 [webhook]"
      - "20:10 [proxy]"
  Combat:
    remove-violations-after: 300
    checks:
      - "Interact"
      - "Killaura"
      - "Aim"
    commands:
      - "20:40 [alert]"
      - "20:40 [log]"
  # Grim2.0.10版本 没有连点器检查，Grim将在未来添加
  Autoclicker:
    remove-violations-after: 300
    checks:
      - "Autoclicker"
    commands:
      - "20:40 [alert]"
      - "20:40 [log]"
