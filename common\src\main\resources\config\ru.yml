# Основная конфигурация GrimAC
# Исходный код: https://github.com/MWHunter/Grim
# Авторское право 2025 DefineOutside и соавторы, лицензия GPLv3.
# Модифицированные двоичные файлы, или плагины со скопированным исходным кодом, должны быть приватными,
# или с полным исходным кодом, доступным покупателям без дополнительной платы.

alerts:
    # Кроме трансляции оповещений игрокам, должны ли они также отправляться на консоль?
    print-to-console: true
    # Это контролирует, будут ли/как будут передаваться оповещения между серверами, подключенными к вашему прокси.
    # У вас должен быть включен 'bungee-plugin-message-channel' в конфигурации Velocity, если используется Velocity.
    proxy:
        # Следует ли отправлять оповещения на другие серверы, подключенные к вашему прокси-серверу?
        send: false
        # Должны ли оповещения, полученные с других серверов, быть объявлены на этом сервере?
        receive: false

verbose:
    print-to-console: false

check-for-updates: true

client-brand:
    # Это означает, что он не будет транслировать свой бренд операторам, если бренд соответствует следующим регексам
    ignored-clients:
        - "^vanilla$"
        - "^fabric$"
        - "^lunarclient:v\\d+\\.\\d+\\.\\d+-\\d{4}$"
        - "^Feather Fabric$"
        - "^labymod$"
    # Grim will blacklist specific Forge versions which include built-in Reach hacks (Forge 1.18.2 to 1.19.3).
    # Setting this option to false will allow said clients to connect to the server. Disable this at your own risk.
    disconnect-blacklisted-forge-versions: true

spectators:
    # Скрыть всех зрителей с разрешением grim.spectator, независимо от того, являются ли они активными зрителями.
    hide-regardless: false
    # Будет делать зрителей скрытыми только в этих мирах. Оставьте пустым, чтобы разрешить все миры
    allowed-worlds:
        - ""

# Сколько времени должно быть у игроков, пока мы не выкинем их за тайм-аут? По умолчанию = 60 секунд
max-transaction-time: 60

# Enable this to stop Grim cancelling pongs received from players.
# This may improve compatibility with other anticheats. It may cause issues with packet limiters.
# Do not enable this if you are not sure what you are doing.
disable-pong-cancelling: false

# Should the duplicate movement packet be cancelled?
# Mojang has fixed this issue in 1.21. This was their attempt to fix the "bucket desync". https://bugs.mojang.com/browse/MC-12363
# This setting only applies to 1.17-1.20.5 clients on 1.8 servers.
cancel-duplicate-packet: true

# Whether or not to ignore the rotation in duplicate packets
ignore-duplicate-packet-rotation: false

Simulation:
    # На сколько мы должны умножить общее преимущество, когда игрок легален.
    # Вот как выглядит конфигурация по умолчанию (ось x = секунды, ось y = 1/1000 блока): https://www.desmos.com/calculator/d4ufgxrxer
    setback-decay-multiplier: 0.999
    # Для какого смещения от движения игрока мы должны создать нарушение?
    # Измеряется в блоках от возможного движения
    # Мы учитываем Optifine, переключая таблицы триггеров, но снижение этого значения до 0.001 уменьшит FastMath
    # будем отмечать античит, если эта компенсация не сработает...
    threshold: 0.001
    # Насколько велико нарушение в тике, прежде чем игрок получит немедленный откат?
    # -1 для отключения
    immediate-setback-threshold: 0.1
    # Насколько велико преимущество над всеми тиками, прежде чем мы начнем делать откат?
    # -1 для отключения
    max-advantage: 1
    # После 50 секунд с конфигурацией по умолчанию игрок перейдет от 4 блоков -> 1 блок преимущества.
    # Это сделано для того, чтобы игрок не собирал слишком много нарушений и никогда не смог очистить их все.
    # Потолок преимущества по умолчанию (ось x = секунды, ось y = 1/1000 блока): https://www.desmos.com/calculator/4lovswdarj
    max-ceiling: 4
    # Порог уровня нарушения для отката
    # 1 для старого поведения
    setback-violation-threshold: 1

# Проверяет, вошел ли игрок в блок во время движения.
Phase:
    setbackvl: 1 # Глитч с блоками может позволить забраться на стену, к тому же эта проверка относительно стабильна
    decay: 0.005

AirLiquidPlace:
    cancelvl: 0

FabricatedPlace:
    cancelvl: 5

FarPlace:
    cancelvl: 5

PositionPlace:
    cancelvl: 5

RotationPlace:
    cancelvl: 5

# Основанная на предсказаниях проверка на отсутствие замедления
# Grim учитывает баги неткода здесь... трудно ошибиться, даже если спамить правым кликом и кнопкой оффхенд.
# Намного стабильнее, чем другие античиты, но, пожалуйста, сообщайте о любых фейлах... Я исправил тонну проблем с неткодом.
NoSlow:
    # Насколько большое смещение является "читерством".
    # По умолчанию это меньше, чем другие смещения
    # Флаги на 0.03-0.2 последовательно при включенном NoSlow
    threshold: 0.001
    # Быстрое отступление на первом пункте, чтобы убрать любое преимущество, которое дает NoSlow
    setbackvl: 5
    # Распад происходит, когда игрок использует предмет И замедляется им
    decay: 0.05

Knockback:
    # На сколько мы должны умножить общее преимущество, когда игрок легален.
    setback-decay-multiplier: 0.999
    # Для какого смещения от движения игрока мы должны создать нарушение?
    # Измеряется в блоках от возможного движения
    threshold: 0.001
    # Насколько велико нарушение в тике, прежде чем игрок получит немедленный откат?
    # -1 для отключения
    immediate-setback-threshold: 0.1
    # Насколько велико преимущество над всеми тиками, прежде чем мы начнем делать откат?
    # -1 для отключения
    max-advantage: 1
    # Это сделано для того, чтобы игрок не собирал слишком много нарушений и никогда не смог очистить их все.
    max-ceiling: 4


Explosion:
    threshold: 0.001
    setbackvl: 3

TimerA:
    setbackvl: 10
    # Миллисекунды, которые игрок может накапливать для последующего использования, когда он отстает.
    # Потенциально может позволить 1.8 обходов: быстрое использование/быстрое исцеление/быстрый лук, если установлено слишком высокое значение, 120 мс кажется хорошим балансом
    drift: 120

# This check limits abuse of the TimerA balance by preventing the player's movement falling too far behind realtime
TimerLimit:
    # Ping at which the check will start to limit timer balance, to prevent abuse.
    # Can cause some setbacks for legitimate players but only if they are over this ping threshold.
    # -1 to disable
    ping-abuse-limit-threshold: 1000

NegativeTimer:
    # Количество миллисекунд, потерянных во время движения, до того, как мы начнем ставить флаг
    drift: 1200

# Тот же метод проверки, что и у TimerA, но для транспортных средств
VehicleTimer:
    # Цель 1.005 таймер
    setbackvl: 10

PacketOrderI:
    # Включить, если игроки помечаются из-за использования модов анимации 1.7
    exempt-placing-while-digging: false

Reach:
    # На сколько мы должны расширить хитбоксы? 0.0005 должно определять 3.0005+ дополнительное достижение
    #
    # Существует 0.03 принудительное расширение с 1.9-1.18.1 (не 1.18.2), или некоторыми комбинациями клиент/сервер из-за
    # изменениями и ограничениями протокола. Эта проверка наиболее эффективна с клиентами 1.7/1.8 на серверах 1.8.
    threshold: 0.0005
    # Должны ли мы отменять удары, которые, как мы знаем, невозможны?
    # Удары 3.00-3.03 могут пройти, но все равно будут замечены из-за ограничений на порядок пакетов.
    block-impossible-hits: true
    # Это отправит дополнительный пакет в конце каждого тика, чтобы увеличить вероятность поимки читов.
    # Это встраивается в список соединений сервера для отправки последнего пакета непосредственно перед тем, как сервер будет удален.
    # Включение этой функции увеличит использование полосы пропускания для всех игроков.
    # Это не снизит общую производительность сервера
    # Если включить эту функцию, будет поймано больше читеров.
    # Если оставить эту функцию отключенной, читеры все равно будут отлавливаться и не будут вызывать ложных срабатываний.
    # Если вы не являетесь PvP-сервером 1.8, этот дополнительный пакет не рекомендуется.
    enable-post-packet: false

exploit:
    allow-sprint-jumping-when-using-elytra: true
    # Эта опция смягчает размещение игрока на блоках-призраках, ресинхронизируя его, когда это происходит.
    allow-building-on-ghostblocks: true
    distance-to-check-if-ghostblocks: 2

# Включить запись в журнал плагинов, которые внедрились в netty при присоединении для отладки проблем совместимости
debug-pipeline-on-join: false

# Включает экспериментальные проверки
experimental-checks: false

reset-item-usage-on-item-update: true
reset-item-usage-on-attack: true
reset-item-usage-on-slot-change: true

# Грим иногда отменяет незаконные пакеты, например, с таймером, после X пакетов в секунду отмененных, когда следует
# нам просто кикнуть игрока? Это необходимо, так как некоторые ограничители пакетов не учитывают пакеты, отмененные Гримом.
packet-spam-threshold: 100
# Включите, чтобы вывести stacktrace, когда игрок кикается из-за packet-spam-threshold
debug-packet-cancel: false

# Grim может обеспечить, чтобы у игрока, выведенного из состояния полёта, пинг не превышал X мс
# Это связано с тем, что Grim пока не проверяет игроков, находящихся в полёте
# Для отключения используйте -1
max-ping-out-of-flying: 1000

# Максимальный пинг при снятии ускорения фейерверком с игрока.
# Это предотвращает возможность для игроков с большим пингом бесконечно использовать один фейерверк‑буст с элитрой.
max-ping-firework-boost: 1000

history:
    enabled: true
    # Сколько записей показывать на странице команды /grim history <player>
    entries-per-page: 15
    # Какое имя сервера вставлять для команды history? Полезно при использовании одной базы для нескольких серверов
    server-name: Prison
    database:
        # Используйте SQLITE для локального хранения или MYSQL для внешней MySQL‑БД. Изменения применяются после перезапуска сервера
        type: SQLITE
        # Параметры подключения MySQL
        host: localhost
        port: 3306
        database: grim
        username: root
        password: ""

config-version: 9
