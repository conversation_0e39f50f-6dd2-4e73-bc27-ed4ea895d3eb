# GrimACのメイン設定
# ソースコード: https://github.com/MWHunter/Grim
# Copyright 2025 DefineOutside and contributors, Licensed under GPLv3.
# 改変されたバイナリやGrimのコードをコピーしたプラグインは、私的なものとするか、
# 購入者に追加料金なしで完全なソースコードを公開する必要があります

# "セットバック"は、Grimによってプレイヤーの違反が検知された際、プレイヤーを元の位置/状態に戻すことを意味します。

alerts:
    # アラートを、プレイヤーに送信するだけでなくコンソールにも送信するかどうか。
    print-to-console: true
    # これは、プロキシに接続された他のサーバー間でアラートが共有される方法を制御します。
    # Velocityを使用している場合は、Velocityの設定で 'bungee-plugin-message-channel' を有効にする必要があります。
    proxy:
        # プロキシに接続された他のサーバーにアラートを送信するかどうか。
        send: false
        # プロキシに接続された他のサーバーからのアラートを受信するかどうか。
        receive: false

verbose:
    print-to-console: false

check-for-updates: true

client-brand:
    # プレイヤーのクライアントブランドが以下の正規表現のいずれかと一致する場合、オペレータにはそのプレイヤーの情報が送信されません。
    ignored-clients:
        - "^vanilla$"
        - "^fabric$"
        - "^lunarclient:v\\d+\\.\\d+\\.\\d+-\\d{4}$"
        - "^Feather Fabric$"
        - "^labymod$"
    # G<PERSON> will blacklist specific Forge versions which include built-in Reach hacks (Forge 1.18.2 to 1.19.3).
    # Setting this option to false will allow said clients to connect to the server. Disable this at your own risk.
    disconnect-blacklisted-forge-versions: true

spectators:
    # 'grim.spectator' パーミッションを持つプレイヤーを、実際にスペクテイター状態でなくても非表示にするかどうか。
    hide-regardless: false
    # スペクテイター状態のプレイヤーを指定したワールドでのみ非表示にする。（空欄にするとすべてのワールドで有効）
    allowed-worlds:
        - ""

# プレイヤーがタイムアウトでKickされるまでの時間を設定します。 | デフォルト= 60(秒)
max-transaction-time: 60

# Enable this to stop Grim cancelling pongs received from players.
# This may improve compatibility with other anticheats. It may cause issues with packet limiters.
# Do not enable this if you are not sure what you are doing.
disable-pong-cancelling: false

# 重複した移動パケットをキャンセルしますか？
# 1.21でこの問題は修正されました。重複パケットの問題は、Mojangが「バケツの同期ズレ」を修正しようとしたことに起因するものでした。 https://bugs.mojang.com/browse/MC-12363
# この設定は、V1.17-1.20.5クライアントが、V1.8サーバーに接続している際にのみ適用されます。
cancel-duplicate-packet: true

# 重複パケットの回転を無視するかどうか。
ignore-duplicate-packet-rotation: false

Simulation:
    # プレイヤーが違反をしていない場合、違反の度合いをどれだけ減らすかの倍率。
    # デフォルト設定のグラフ（x軸 = 秒, y軸 = 1/1000ブロック）: https://www.desmos.com/calculator/d4ufgxrxer
    setback-decay-multiplier: 0.999
    # プレイヤーの動きが、シミュレーションからどれだけズレた場合に違反とみなすか。
    # シミュレーションされた動きからのズレをブロック単位で設定します。
    # OptifineのFastMath(計算の最適化)に対応するために計算テーブルを切り替え、0.001に下げるとFastMathによる誤検知を軽減します。
    # ただし、この補正がうまく機能しない場合、Grimがプレイヤーを不正と判断する可能性があります...
    threshold: 0.001
    # プレイヤーを即座にセットバックする前に、シミュレーションされた動きとプレイヤーの実際の動きの間にどれだけの違反が必要かを指定します。
    # -1で無効化できます。
    immediate-setback-threshold: 0.1
    # 全ティックでのシミュレーションとの累積ズレがこの値を超えたらリセットを開始します。
    # -1で無効化できます。
    max-advantage: 1
    # デフォルト設定では、50秒経過すると、プレイヤーのアドバンテージが4ブロックから1ブロックに減少します。
    # これは、プレイヤーが違反を蓄積しすぎて、違反を解消できなくなるのを防ぐためです。
    # デフォルトのアドバンテージの上限 (x軸 = 秒, y軸 = 1/1000ブロック): https://www.desmos.com/calculator/4lovswdarj
    max-ceiling: 4
    # セットバックの違反レベル閾値
    # 旧仕様の挙動は 1 です
    setback-violation-threshold: 1

# プレイヤーが移動中にブロックに入ったかどうかをチェックします。
Phase:
    setbackvl: 1 # 1ブロック以上のズレがある場合に違反と判断します。
    decay: 0.005

AirLiquidPlace:
    cancelvl: 0

FabricatedPlace:
    cancelvl: 5

FarPlace:
    cancelvl: 5

PositionPlace:
    cancelvl: 5

RotationPlace:
    cancelvl: 5

# 予測に基づく「NoSlow」チェック
# Grimは、バグのあるネットコードを考慮しており、右クリックやオフハンドボタンを連打しても誤検知が起こりにくくなっています。
# 他のアンチチートよりも安定していますが、もし誤検知があれば報告してください。ネットコードの問題を多数修正しています。
NoSlow:
    # どれだけのズレが「チート」として扱われるか
    # デフォルトで他のズレ基準よりも低めに設定されています
    # NoSlow（速度低下なし）の状態では、0.03〜0.2の範囲で一貫してフラグが立ちます
    threshold: 0.001
    # NoSlowによるアドバンテージを無効化するため、最初の項目で即座にセットバックさせます
    setbackvl: 5
    # プレイヤーがアイテム使用時に速度低下が発生する場合、この値によりアドバンテージの累積が減少します
    decay: 0.05

Knockback:
    # プレイヤーが違反していない場合、総アドバンテージにどれだけの倍率を掛けるか
    setback-decay-multiplier: 0.999
    # プレイヤーの速度からどれだけズレたら違反とみなすか
    # 許容速度からのズレをブロック単位で測定します
    threshold: 0.001
    # プレイヤーが即座にセットバックされる前に、1ティックで発生しなければならない違反のしきい値
    # -1で無効化できます
    immediate-setback-threshold: 0.1
    # 全ティックでの累積アドバンテージがこの値を超えたらセットバックを開始します
    # -1で無効化できます
    max-advantage: 1
    # 違反が過剰に蓄積し、リセットが難しくなるのを防ぐための上限です
    max-ceiling: 4

Explosion:
    threshold: 0.001
    setbackvl: 3

TimerA:
    setbackvl: 10
    # プレイヤーがネットワーク遅延やラグの影響で操作がサーバーの予測より遅れている場合、
    # その遅れ分を後で使用できるように蓄積するためのミリ秒数を設定します。
    # この値を高く設定しすぎると、1.8版の高速使用、高速回復、高速弓などの不正な動作をバイパスできる可能性があるため、
    # 120ミリ秒がバランスの良い設定とされています。
    drift: 120

# This check limits abuse of the TimerA balance by preventing the player's movement falling too far behind realtime
TimerLimit:
    # タイマーバランスの制限を開始し、不正利用を防止するためのPingのしきい値
    # 正当なプレイヤーであっても、このPingのしきい値を超えるとセットバックが発生する場合があります
    # -1で無効化
    ping-abuse-limit-threshold: 1000

NegativeTimer:
    # プレイヤーが移動中にどれだけミリ秒の遅延が発生した場合にフラグを立て始めるかを指定します
    # ここで指定した遅延量（ミリ秒）を超えると、違反として検知される可能性があります
    drift: 1200

# TimerAと同じチェック方法ですが、乗り物操作に適用されます
VehicleTimer:
    # 目標タイマー値は1.005です
    setbackvl: 10

PacketOrderI:
    # プレイヤーが1.7のアニメーションMODを使用したことで検出されている場合に有効にします
    exempt-placing-while-digging: false

Reach:
    # ヒットボックスをどれだけ拡大するかを設定します。0.0005に設定することで3.0005以上のリーチを検出できます
    #
    # プロトコルの変更や制限により、1.9〜1.18.1（1.18.2を除く）や
    # 一部のクライアント/サーバーの組み合わせでは、強制的に0.03の拡大が適用される場合があります。
    # このチェックは1.7/1.8クライアントが1.8サーバーに接続した際に最も効果的です。
    threshold: 0.0005
    # 不可能なヒット（距離的に届かない攻撃）をキャンセルするか
    # 3.00〜3.03のリーチによるヒットが通る場合がありますが、パケット順序の制限によりフラグが立つこともあります
    block-impossible-hits: true
    # チート検出の可能性を高めるため、各ティックの終わりに追加のパケットを送信します
    # これはサーバーの接続リストに挿入され、サーバーがフラッシュする直前に送信されます
    # 有効にすると全プレイヤーの帯域幅使用量が増加しますが、サーバーのパフォーマンスは低下しません
    # 有効にすることでさらに多くのチーターを検出できますが、
    # 無効のままでもチーターを検出でき、誤検知も発生しません
    # PvP特化の1.8サーバーでない限り、この追加パケットの有効化は推奨されません
    enable-post-packet: false

exploit:
    # エリトラでダッシュジャンプを行うと非常に高い速度を得られるエクスプロイト（意図しない挙動）があります。
    # この設定をfalseにすると、そのエクスプロイトを防ぎます
    # Mojangのネットコードにより、エリトラの動作は開始がクライアント側、終了がサーバー側で処理されるためにズレが生じています。
    # エリトラは水平摩擦0.99を持ち、水平速度0.2が常に追加されると非常に高速度になります。
    allow-sprint-jumping-when-using-elytra: true

    # このオプションは、ゴーストブロックが発生したときにプレイヤーを再同期させることで、
    # ゴーストブロックへのブロック設置をできるだけ制限します
    allow-building-on-ghostblocks: true

    # 上記の設定に基づき、ゴーストブロックのチェック距離を定義します
    # 設定可能な範囲は2から4です
    distance-to-check-if-ghostblocks: 2

# Nettyに参加時にプラグインが注入された場合の互換性問題をデバッグするためにログを有効にします
debug-pipeline-on-join: false

# 実験的なチェックを有効にします
experimental-checks: false

reset-item-usage-on-item-update: true
reset-item-usage-on-attack: true
reset-item-usage-on-slot-change: true

# Grimは "Timer" のような違反パケットをキャンセルすることがありますが、1秒間にキャンセルされたパケット数がXを超えた場合、
# プレイヤーを強制的にキックしますか？これは、いくつかのパケットリミッターがGrimによってキャンセルされたパケットをカウントしないためです。
packet-spam-threshold: 100
# packet-spam-threshold によりプレイヤーがキックされたときにスタックトレースを出力する場合は有効にする
debug-packet-cancel: false

# Grimは、飛行状態を解除されたプレイヤーがXミリ秒以上のPingを持つことを許可しません
# これは、現在Grimが飛行中のプレイヤーをチェックしていないためです
# 無効にするには、-1を設定してください
max-ping-out-of-flying: 1000

# プレイヤーから、ロケット花火による加速効果を削除する際の最大ピン値
# これにより、レイテンシの高いプレイヤーが1つのロケット花火による加速でエリトラを永久に使用するのを防ぎます。
max-ping-firework-boost: 1000

history:
    enabled: true
    # /grim history <player> で 1 ページに表示するエントリー数
    entries-per-page: 15
    # history コマンドで挿入されるサーバー名は？ 複数サーバーで同じデータベースを使用する場合に便利
    server-name: Prison
    database:
        # ローカル保存には SQLITE、外部 MySQL データベースには MYSQL を使用します。サーバー再起動時にのみ反映
        type: SQLITE
        # MySQL 接続情報
        host: localhost
        port: 3306
        database: grim
        username: root
        password: ""

config-version: 9
