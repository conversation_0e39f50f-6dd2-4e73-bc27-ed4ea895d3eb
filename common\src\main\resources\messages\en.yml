# \u00BB is » (double >>), ANSI and UTF-8 interpret this differently... you may even see ? due to this
prefix: "&bGrim &8\u00BB"

alerts-enabled: "%prefix% &fAlerts enabled"
alerts-disabled: "%prefix% &fAlerts disabled"
verbose-enabled: "%prefix% &fV<PERSON>bose enabled"
verbose-disabled: "%prefix% &fV<PERSON>bose disabled"
brands-enabled: "%prefix% &fBrands enabled"
brands-disabled: "%prefix% &fBrands disabled"
client-brand-format: "%prefix% &f%player% joined using %brand%"
console-specify-target: "%prefix% &cYou must specify a target as the console!"
reloading: "%prefix% &7Reloading config..."
reloaded: "%prefix% &fConfig has been reloaded."
reload-failed: "%prefix% &cFailed to reload config."
player-not-found: "%prefix% &cPlayer is exempt or offline!"
player-not-this-server: "%prefix% &cPlayer is not on this server!"
spectate-return: "<click:run_command:/grim stopspectating><hover:show_text:\"/grim stopspectating\">\n%prefix% &fClick here to return to previous location\n</hover></click>"
cannot-spectate-return: "%prefix% &cYou can only do this after spectating a player"
cannot-run-on-self: "%prefix% &cYou cannot use this command on yourself!"
upload-log: "%prefix% &fUploaded debug to: %url%"
upload-log-start: "%prefix% &fUploading log... please wait"
upload-log-not-found: "%prefix% &cUnable to find that log"
upload-log-upload-failure: "%prefix% &cSomething went wrong while uploading this log, see console for more info"
disconnect:
    timeout: "<lang:disconnect.timeout>"
    closed: "<lang:disconnect.closed>"
    error: "<red>An error occurred whilst processing packets. Please contact the administrators."
    blacklisted-forge: "<red>Your forge version is blacklisted due to inbuilt reach hacks.<newline><gold>Versions affected: 1.18.2-1.19.3<newline><newline><red>Please see https://github.com/MinecraftForge/MinecraftForge/issues/9309."
run-as-player: "%prefix% &cThis command can only be used by players!"
run-as-player-or-console: "%prefix% &cThis command can only be used by players or the console!"

# Valid placeholders:
# %prefix%
# %player%
# %check_name%
# %description%
# %experimental%
# %vl% - violations
# %verbose% - extra information from the check such as offsets, not all checks will add information
alerts-format: "%prefix% &f%player% &bfailed &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
alerts-format-proxy: "%prefix% &f[&cproxy&f] &f%player% &bfailed &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
experimental-symbol: "*"

profile:
    - "&7======================"
    - "%prefix% &bProfile for &f%player%"
    - "&bPing: &f%ping%"
    - "&bVersion: &f%version%"
    - "&bClient Brand: &f%brand%"
    - "&bHorizontal Sensitivity: &f%h_sensitivity%%"
    - "&bVertical Sensitivity: &f%v_sensitivity%%"
    - "&bFastMath Enabled: &f%fast_math%"
    - "&7======================"
help:
    - "&7======================"
    - "/grim alerts &f- &7Toggle alerts"
    - "/grim brands &f- &7Toggle brands"
    - "/grim profile <player> &f- &7View player info"
    - "/grim help &f- &7View this help message"
    - "/grim debug <player> &f- &7Developer Prediction output"
    - "/grim perf &f- &7Developer ms/prediction"
    - "/grim reload &f- &7Reloads the config"
    - "/grim spectate <player> &f- &7Spectate a player"
    - "/grim verbose &f- &7Shows every flag to you, without buffers"
    - "/grim log [0-255] &f- &7Uploads a debug log for prediction flags"
    - "/grim history <player> [page] &f- &7Shows previous alerts for the player"
    - "&7======================"
    -
grim-history-load-failure: "%prefix% &cHistory subsystem failed to load! Check server console for errors."
grim-history-disabled: "%prefix% &cHistory subsystem is disabled!"
# Valid placeholders: %prefix% %player% %page% %maxPages%
grim-history-header: "%prefix% &bShowing logs for &f%player% (&f%page%&b/&f%maxPages%&f)"
# Valid placeholders: %prefix% %grim_version% %client_brand% %client_version% %server_version% %check% %vl% %verbose% %timeago% %server%
grim-history-entry: "%prefix% &8[&f%server%&8] &bFailed &f%check% (x&c%vl%&f) &7%verbose% (&b%timeago% ago&7)"
