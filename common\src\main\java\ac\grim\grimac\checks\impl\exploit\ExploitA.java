package ac.grim.grimac.checks.impl.exploit;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.payload.PayloadItemName;
import com.github.retrooper.packetevents.event.PacketReceiveEvent;
import com.github.retrooper.packetevents.protocol.packettype.PacketType;
import com.github.retrooper.packetevents.protocol.player.ClientVersion;
import com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientNameItem;
import com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPluginMessage;
import org.jetbrains.annotations.NotNull;

@CheckData(name = "ExploitA", description = "Too long item name in anvil")
public class ExploitA extends Check implements PacketCheck {
    public ExploitA(GrimPlayer player) {
        super(player);
    }

    @Override
    public void onPacketReceive(PacketReceiveEvent event) {
        if (event.getPacketType() == PacketType.Play.Client.NAME_ITEM) {
            check(new WrapperPlayClientNameItem(event).getItemName(), event);
        }

        if (event.getPacketType() == PacketType.Play.Client.PLUGIN_MESSAGE && player.getClientVersion().isOlderThan(ClientVersion.V_1_13)) {
            WrapperPlayClientPluginMessage wrapper = new WrapperPlayClientPluginMessage(event);
            if (wrapper.getChannelName().equals("MC|ItemName")) {
                check(PayloadItemName.CODEC.read(wrapper.getData()).itemName(), event);
            }
        }
    }

    private void check(@NotNull String name, PacketReceiveEvent event) {
        int limit = player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_17) ? 50
                : player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_12) ? 35
                : player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_11_1) ? 31 : 30;

        if (name.length() > limit) {
            if (flagAndAlert("name=" + name) && shouldModifyPackets()) {
                event.setCancelled(true);
                player.onPacketCancel();
            }
        }
    }
}
