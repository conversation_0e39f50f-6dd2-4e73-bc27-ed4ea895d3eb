# Configuration principale de GrimAC
# Code source : https://github.com/MWHunter/Grim
# Copyright 2025 DefineOutside et contributeurs, sous licence GPLv3.
# Les binaires modifiés, ou les plugins contenant du code grim copié, doivent être privés,
# ou avec le code source complet disponible pour les acheteurs sans frais supplémentaires.

alerts:
    # En plus de diffuser des alertes aux joueurs, doivent-elles également être afffichées dans console ?
    print-to-console: true
    # Cela contrôle la manière dont les alertes sont partagées entre les serveurs connectés à votre proxy.
    # Vous devez avoir activé 'bungee-plugin-message-channel' dans la configuration de Velocity si Velocity est utilisé.
    proxy:
        # Les alertes doivent-elles être envoyées aux autres serveurs connectés à votre proxy ?
        send: false
        # Les alertes reçues des autres serveurs doivent-elles être annoncées sur ce serveur ?
        receive: false

verbose:
    print-to-console: false

check-for-updates: true

client-brand:
    # Cela signifie qu'il n'enverra pas la nature du client aux opérateurs si il correspond aux clients suivants
    ignored-clients:
        - "^vanilla$"
        - "^fabric$"
        - "^lunarclient:v\\d+\\.\\d+\\.\\d+-\\d{4}$"
        - "^Feather Fabric$"
        - "^labymod$"
    # Grim will blacklist specific Forge versions which include built-in Reach hacks (Forge 1.18.2 to 1.19.3).
    # Setting this option to false will allow said clients to connect to the server. Disable this at your own risk.
    disconnect-blacklisted-forge-versions: true

spectators:
    # Masquer tout les spectateurs ayant la permission grim.spectator, peu importe s'ils sont actuellement en train d'observer.
    hide-regardless: false
    # Les spectateurs seront cachés uniquement dans ces mondes, laissez vide pour les afficher dans tous les mondes.
    allowed-worlds:
        - ""

# Au bout de combien de temps les joueurs doivent-ils être expulsés en cas de perte de connexion ? Défault = 60 secondes
max-transaction-time: 60

# Enable this to stop Grim cancelling pongs received from players.
# This may improve compatibility with other anticheats. It may cause issues with packet limiters.
# Do not enable this if you are not sure what you are doing.
disable-pong-cancelling: false

# Should the duplicate movement packet be cancelled?
# Mojang has fixed this issue in 1.21. This was their attempt to fix the "bucket desync". https://bugs.mojang.com/browse/MC-12363
# This setting only applies to 1.17-1.20.5 clients on 1.8 servers.
cancel-duplicate-packet: true

# Whether or not to ignore the rotation in duplicate packets
ignore-duplicate-packet-rotation: false

Simulation:
    # De combien devons-nous multiplier l'avantage total lorsque le joueur est légitime ?
    # Voici à quoi ressemble la configuration par défaut (l'axe x = secondes, l'axe y = 1/1000 de bloc) : https://www.desmos.com/calculator/d4ufgxrxer
    setback-decay-multiplier: 0.999
    # Quel est l'écart maximal par rapport au mouvement du joueur pour lequel nous devrions créer une violation ?
    # Mesuré en blocs par rapport au mouvement possible.
    # Nous prenons en compte Optifine en changeant les tables trigonométriques, mais en réduisant cela à 0.001, cela réduira les signalements de l'anticheat
    # sur FastMath si cette compensation ne fonctionne pas...
    threshold: 0.001
    # Quelle est la distance maximale d'une violation par tick avant que le joueur ne soit immédiatement renvoyé en arrière ?
    # -1 pour désactiver
    immediate-setback-threshold: 0.1
    # Quelle est la distance maximale d'une violation sur tout les ticks avant que l'anti-cheat commence à renvoyer en arrière ?
    # -1 pour désactiver
    max-advantage: 1
    # Après 50 secondes avec la configuration par défaut, le joueur passera de 4 blocs -> 1 bloc d'avantage
    # Cela vise à empêcher le joueur d'accumuler trop de violations et de ne jamais pouvoir toutes les réinitialiser.
    # Plafond d'avantage par défaut (l"axe x = secondes, l'axe y = 1/1000 de bloc)) : https://www.desmos.com/calculator/4lovswdarj
    max-ceiling: 4
    # Seuil du niveau de violation pour le setback
    # 1 pour le comportement ancien
    setback-violation-threshold: 1

# Vérifications pour voir si un joueur est entré dans un bloc pendant un mouvement.
Phase:
    setbackvl: 1 # Rentrer dans des blocs peut permettre de grimper sur les murs, et cette vérification est relativement stable.
    decay: 0.005

AirLiquidPlace:
    cancelvl: 0

FabricatedPlace:
    cancelvl: 5

FarPlace:
    cancelvl: 5

PositionPlace:
    cancelvl: 5

RotationPlace:
    cancelvl: 5

# Vérification de no-slow basée sur la prédiction.
# Grim tient compte des problèmes de netcode ici... difficile à tromper même en spamant le clic droit et le bouton de la main secondaire.
# Bien plus stable que les autres anti-triche, mais veuillez signaler toute fausse détection... J'ai corrigé de nombreux problèmes de netcode ici.
NoSlow:
    # Quel est le niveau d'écart considéré comme "triche"
    # Par défaut, cela est inférieur à d'autres écarts.
    # Génère des avertissements avec des valeurs comprises entre 0,03 et 0,2 lors de l'utilisation de NoSlow
    threshold: 0.001
    # Renvoyer en arrière sur le premier objet pour éliminer tout avantage donné par NoSlow
    setbackvl: 5
    # Décroissance lorsque le joueur utilise un objet ET est ralenti par celui-ci.
    decay: 0.05

Knockback:
    # De combien devons-nous multiplier l'avantage total lorsque le joueur est légitime ?
    setback-decay-multiplier: 0.999
    # Quel est l'écart maximal par rapport au mouvement du joueur pour lequel nous devrions créer une violation ?
    # Mesuré en blocs par rapport au mouvement possible.
    threshold: 0.001
    # Quelle est la distance maximale d'une violation par tick avant que le joueur ne soit immédiatement renvoyé en arrière ?
    # -1 pour désactiver
    immediate-setback-threshold: 0.1
    # Quelle est la distance maximale d'une violation sur tout les ticks avant que l'anti-cheat commence à renvoyer en arrière ?
    # -1 pour désactiver
    max-advantage: 1
    # Cela vise à empêcher le joueur d'accumuler trop de violations et de ne jamais pouvoir toutes les réinitialiser.
    max-ceiling: 4


Explosion:
    threshold: 0.001
    setbackvl: 3

TimerA:
    setbackvl: 10
    # Le nombre de millisecondes que le joueur peut accumuler pour une utilisation ultérieure lorsqu'il prend du retard.
    # Si la valeur est trop élevée, cela pourrait potentiellement permettre de contourner les mécaniques 1.8, comme l'utilisation rapide, la guérison rapide et le tir à l'arc rapide. Une valeur de 120 ms semble être un bon équilibre.
    drift: 120

# This check limits abuse of the TimerA balance by preventing the player's movement falling too far behind realtime
TimerLimit:
    # Ping at which the check will start to limit timer balance, to prevent abuse.
    # Can cause some setbacks for legitimate players but only if they are over this ping threshold.
    # -1 to disable
    ping-abuse-limit-threshold: 1000

NegativeTimer:
    # Le nombre de millisecondes perdus pendant le déplacement avant de commencer à signaler des infractions.
    drift: 1200

# Même méthode de vérification que TimerA, mais pour les véhicules.
VehicleTimer:
    # Cibler un chronomètre de 1,005.
    setbackvl: 10

PacketOrderI:
    # Activer si les joueurs sont signalés pour l'utilisation de mods d'animation 1.7
    exempt-placing-while-digging: false

Reach:
    # De combien devrions-nous agrandir les hitboxes ? Une augmentation de 0,0005 devrait détecter une portée de 3,0005 ou plus.
    #
    # Il y a un agrandissement forcé de 0,03 avec les versions 1.9 à 1.18.1 (pas 1.18.2) ou certaines combinaisons client/serveur en raison
    # des changements et limitations du protocole. Cette vérification est particulièrement puissante avec les clients 1.7/1.8 sur les serveurs 1.8.
    threshold: 0.0005
    # Devons-nous annuler les coups que nous savons impossibles ?
    # Les coups entre 3,00 et 3,03 peuvent passer mais seront quand même signalés en raison des limitations de l'ordre des paquets.
    block-impossible-hits: true
    # Cela enverra un paquet supplémentaire à la fin de chaque tick pour augmenter les chances de détecter les triches.
    # Cela s'injecte dans la liste de connexions du serveur pour envoyer un dernier paquet juste avant que le serveur ne le vide.
    # Activer cette fonctionnalité augmentera l'utilisation de la bande passante pour tous les joueurs.
    # Cela n'entraînera pas de diminution des performances globales du serveur.
    # Activer cette fonctionnalité permettra de détecter davantage de tricheurs.
    # Laisser cette fonctionnalité désactivée permettra tout de même de détecter les tricheurs et n'entraînera pas de faux positifs.
    # À moins d'être un serveur axé sur le PvP en 1.8, l'envoi de ce paquet supplémentaire n'est pas recommandé.
    enable-post-packet: false

exploit:
    allow-sprint-jumping-when-using-elytra: true
    # Cette option atténue le placement du joueur sur les ghostblocks en resynchronisant le joueur lorsque cela se produit.
    allow-building-on-ghostblocks: true
    distance-to-check-if-ghostblocks: 2

# Activer l'enregistrement des plugins ayant injecté dans Netty lors de la connexion pour déboguer les problèmes de compatibilité
debug-pipeline-on-join: false

# Active les vérifications expérimentales
experimental-checks: false

reset-item-usage-on-item-update: true
reset-item-usage-on-attack: true
reset-item-usage-on-slot-change: true

# Grim annule parfois des paquets illégaux, comme ceux liés au chronomètre, après avoir annulé X paquets en une seconde,
# à partir de combien de paquets annulés devrions-nous simplement expulser le joueur ?
# Cela est nécessaire car certains limiteurs de paquets ne comptent pas les paquets annulés par Grim.
packet-spam-threshold: 100
# Activez ceci pour afficher une trace de pile lorsqu’un joueur est expulsé à cause du packet-spam-threshold
debug-packet-cancel: false

# Grim peut faire en sorte qu’un joueur sorti de l’état de vol n’ait pas plus de X ms de ping
# Cela est dû au fait que Grim ne vérifie pas encore les joueurs en vol
# Pour désactiver, utilisez -1
max-ping-out-of-flying: 1000

# Ping maximal lorsque la propulsion de feu d’artifice est retirée du joueur.
# Cela empêche les joueurs à forte latence d’utiliser indéfiniment un seul boost de feu d’artifice avec une élytra.
max-ping-firework-boost: 1000

history:
    enabled: true
    # Combien d’entrées doivent être affichées par page avec /grim history <player>
    entries-per-page: 15
    # Quel nom de serveur doit être inséré pour la commande history ? Utile si vous utilisez la même base de données pour plusieurs serveurs
    server-name: Prison
    database:
        # Utilisez SQLITE pour un stockage local, MYSQL si vous disposez d’une base MySQL externe. Mis à jour seulement au redémarrage du serveur
        type: SQLITE
        # Détails de connexion MySQL
        host: localhost
        port: 3306
        database: grim
        username: root
        password: ""

config-version: 9
