# %check_name% - <PERSON><PERSON><PERSON><PERSON><PERSON> ismi
# %description% - <PERSON><PERSON><PERSON><PERSON><PERSON> açıklamas<PERSON>
# %vl% - Ya<PERSON><PERSON><PERSON> ihlaller
# %verbose% - Ekstra bilgilendirme
# %player% - Oyuncu ismi
# [alert] - Uyarılar için bunu kullanın
# [webhook] - Discord webhook için bunu kullanın
# [proxy] - Proxy'nize bağlı diğer sunuculara uyarı göndermek için bunu kullanın (BungeeCord/Velocity)
Punishments:
  Simulation:
    # Kaç saniye sonra ihlaller kaldırılsın?
    remove-violations-after: 300
    # <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>, aşağıdaki adlara sahip tüm kontrollerle eşleşecektir,
    # Aksi takdirde eşleşecek bir denetimi hariç tutmak için, isminin önüne ünlem işareti koyun
    # Örnek olarak, !BadPacketsN
    checks:
      - "Simulation"
      - "GroundSpoof"
      - "Timer"
      - "TimerLimit"
      - "NoFall"
    # Eşik:Ara<PERSON>ık Komutu
    #
    # Örnek: 100 ihlalde "yanlış hareket!" mesajıyla oyuncuyu atmak için, aralık komutu olmadan:
    # - "100:0 kick %player% Yanlış hareket!"
    # 0, tam olarak bir kez çalıştırılmasını ifade eder
    # - "100:50 say %player% Hile yapıyor!"
    # Kullanıcı 100 işaretine ulaştığında çalışır ve bundan sonra, 100'ün üzerindeki her 50. işarette çalışır
    commands:
      - "100:40 [alert]"
      - "100:40 [log]"
      - "100:100 [webhook]"
      - "100:100 [proxy]"
  Knockback:
    remove-violations-after: 300
    checks:
      - "Knockback"
      - "Explosion"
    commands:
      - "5:5 [alert]"
      - "5:5 [log]"
      - "20:20 [webhook]"
      - "20:20 [proxy]"
  Post:
    remove-violations-after: 300
    checks:
      - "Post"
    commands:
      - "20:20 [alert]"
      - "20:20 [log]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  BadPackets:
    remove-violations-after: 300
    checks:
      - "BadPackets"
      - "PacketOrder"
      - "Crash"
    commands:
      - "20:20 [alert]"
      - "20:20 [log]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  Reach:
    remove-violations-after: 300
    checks:
      - "Reach"
    commands:
      - "1:1 [alert]"
      - "1:1 [log]"
      - "1:1 [webhook]"
      - "1:1 [proxy]"
  Hitboxes:
    remove-violations-after: 300
    checks:
      - "Hitboxes"
    commands:
      - "5:3 [alert]"
      - "5:3 [log]"
      - "5:3 [webhook]"
      - "5:3 [proxy]"
  Misc:
    remove-violations-after: 300
    checks:
      - "Vehicle"
      - "NoSlow"
      - "Sprint"
      - "MultiActions"
      - "Place"
      - "Baritone"
      - "Break"
      - "TransactionOrder"
      - "Elytra"
      - "Chat"
      - "Exploit"
    commands:
      - "10:5 [alert]"
      - "10:5 [log]"
      - "20:10 [webhook]"
      - "20:10 [proxy]"
  Combat:
    remove-violations-after: 300
    checks:
      - "Interact"
      - "Killaura"
      - "Aim"
    commands:
      - "20:40 [alert]"
      - "20:40 [log]"
  # 2.2.10 itibarıyla, AutoClicker denetimleri yoktur ve bu bir yer tutucudur. Grim, gelecekte AutoClicker denetimlerini dahil edecektir.
  Autoclicker:
    remove-violations-after: 300
    checks:
      - "Autoclicker"
    commands:
      - "20:40 [alert]"
      - "20:40 [log]"
