# \u00BB это " (двойной >>), ANSI и UTF-8 интерпретируют это по-разному... вы можете даже увидеть "?" из-за этого
prefix: "&bGrim &8\u00BB"

alerts-enabled: "%prefix% &fОповещения включены"
alerts-disabled: "%prefix% &fОповещения отключены"
verbose-enabled: "%prefix% &fVerbose enabled"
verbose-disabled: "%prefix% &fVerbose disabled"
brands-enabled: "%prefix% &fBrands enabled"
brands-disabled: "%prefix% &fBrands disabled"
client-brand-format: "%prefix% &f%player% присоединился, используя %brand%"
console-specify-target: "%prefix% &cВы должны указать цель в качестве консоли!"
reloading: "%prefix% &7Reloading config..."
reloaded: "%prefix% &fConfig has been reloaded."
reload-failed: "%prefix% &cFailed to reload config."
player-not-found: "%prefix% &cИгрок исключен или находится вне сети!"
player-not-this-server: "%prefix% &cИгрок не находится на этом сервере!"
spectate-return: "<click:run_command:/grim stopspectating><hover:show_text:\"/grim stopspectating\">\n%prefix% &fНажмите здесь, чтобы вернуться на предыдущее место\n</hover></click>"
cannot-spectate-return: "%prefix% &cВы можете сделать это только после наблюдения за игроком"
cannot-run-on-self: "%prefix% &cВы не можете использовать эту команду на себе!"
upload-log: "%prefix% &fОтладка была загружена на: %url%"
upload-log-start: "%prefix% &fЗагрузка журнала... пожалуйста, подождите"
upload-log-not-found: "%prefix% &cНевозможно найти этот журнал"
upload-log-upload-failure: "%prefix% &cЧто-то пошло не так при загрузке этого журнала, смотрите консоль для получения дополнительной информации"
disconnect:
    timeout: "<lang:disconnect.timeout>"
    closed: "<lang:disconnect.closed>"
    error: "<red>An error occurred whilst processing packets. Please contact the administrators."
    blacklisted-forge: "<red>Your forge version is blacklisted due to inbuilt reach hacks.<newline><gold>Versions affected: 1.18.2-1.19.3<newline><newline><red>Please see https://github.com/MinecraftForge/MinecraftForge/issues/9309."
run-as-player: "%prefix% &cЭту команду могут использовать только игроки!"
run-as-player-or-console: "%prefix% &cЭту команду могут использовать только игроки или консоль!"

# Допустимые заполнители:
# %prefix%
# %player%
# %check_name%
# %description%
# %experimental%
# %vl% - нарушения
# %verbose% - дополнительная информация от проверки, например, смещения, не все проверки добавляют информацию.
alerts-format: "%prefix% &f%player% &bпровалил &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
alerts-format-proxy: "%prefix% &f[&cproxy&f] &f%player% &bпровалил &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
experimental-symbol: "*"

profile:
    - "&7======================"
    - "%prefix% &bПрофиль для &f%player%"
    - "&bПинг: &f%ping%"
    - "&bВерсия: &f%version%"
    - "&bБренд клиента: &f%brand%"
    - "&bГоризонтальная чувствительность: &f%h_sensitivity%%"
    - "&bВертикальная чувствительность: &f%v_sensitivity%%"
    - "&bFastMath Включено: &f%fast_math%"
    - "&7======================"
help:
    - "&7======================"
    - "/grim alerts &f- &7Включить/выключить оповещения"
    - "/grim brands &f- &7Toggle brands"
    - "/grim profile <игрок> &f- &7Просмотреть информацию об игроке"
    - "/grim help &f- &7Просмотреть это сообщение помощи"
    - "/grim debug <игрок> &f- &7Вывод прогноза разработчика"
    - "/grim perf &f- &7Вывод производительности Grim для разработчиков"
    - "/grim reload &f- &7Перезагружает конфигурацию"
    - "/grim spectate <игрок> &f- &7Наблюдать за игроком"
    - "/grim verbose &f- &7Показывает все флаги без буферов"
    - "/grim log [0-255] &f- &7Загружает журнал отладки для флагов предсказания"
    - "/grim history <player> [страница] &f- &7Показывает предыдущие предупреждения игрока"
    - "&7======================"

grim-history-load-failure: "%prefix% &cНе удалось загрузить подсистему истории! Проверьте консоль сервера на наличие ошибок."
grim-history-disabled: "%prefix% &cПодсистема истории отключена!"
# Valid placeholders: %prefix% %player% %page% %maxPages%
grim-history-header: "%prefix% &bПоказ журналов для &f%player% (&f%page%&b/&f%maxPages%&f)"
# Valid placeholders: %prefix% %grim_version% %client_brand% %client_version% %server_version% %check% %vl% %verbose% %timeago% %server%
grim-history-entry: "%prefix% &8[&f%server%&8] &bПровалено &f%check% (x&c%vl%&f) &7%verbose% (&b%timeago% назад&7)"
