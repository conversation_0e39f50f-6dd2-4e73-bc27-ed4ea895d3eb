# \u00BB se traduit par » (double >>). Les interprétations diffèrent entre les normes ANSI et UTF-8... vous pouvez même voir ? en raison de cela
prefix: "&bGrim &8\u00BB"

alerts-enabled: "%prefix% &fAlertes activées"
alerts-disabled: "%prefix% &fAlertes désactivées"
verbose-enabled: "%prefix% &fVerbose enabled"
verbose-disabled: "%prefix% &fVerbose disabled"
brands-enabled: "%prefix% &fBrands enabled"
brands-disabled: "%prefix% &fBrands disabled"
client-brand-format: "%prefix% &f%player% a rejoint en utilisant %brand%"
console-specify-target: "%prefix% &cVous devez spécifier une cible en tant que console !"
reloading: "%prefix% &7Reloading config..."
reloaded: "%prefix% &fConfig has been reloaded."
reload-failed: "%prefix% &cFailed to reload config."
player-not-found: "%prefix% &cPlayer est ignoré ou hors ligne !"
player-not-this-server: "%prefix% &cPlayer n'est pas sur ce serveur !"
spectate-return: "<click:run_command:/grim stopspectating><hover:show_text:\"/grim stopspectating\">\n%prefix% &fClickez ici pour retourner à votre position d'origine\n</hover></click>"
cannot-spectate-return: "%prefix% &cVous pouvez faire cela uniquement en étant en spectateur"
cannot-run-on-self: "%prefix% &cVous ne pouvez pas utiliser cette commande sur vous-même !"
upload-log: "%prefix% &fLe fichier de débogage a été téléversé vers : %url%"
upload-log-start: "%prefix% &fTéléversement du journal... Veuillez patienter"
upload-log-not-found: "%prefix% &cUImpossible de trouver le journal de débogage"
upload-log-upload-failure: "%prefix% &cUne erreur est survenue lors du téléversement de ce journal, regardez la console pour plus d'information"
disconnect:
    timeout: "<lang:disconnect.timeout>"
    closed: "<lang:disconnect.closed>"
    error: "<red>An error occurred whilst processing packets. Please contact the administrators."
    blacklisted-forge: "<red>Your forge version is blacklisted due to inbuilt reach hacks.<newline><gold>Versions affected: 1.18.2-1.19.3<newline><newline><red>Please see https://github.com/MinecraftForge/MinecraftForge/issues/9309."
run-as-player: "%prefix% &cCette commande ne peut être utilisée que par les joueurs !"
run-as-player-or-console: "%prefix% &cCette commande ne peut être utilisée que par les joueurs ou la console !"

# Placeholders valides :
# %prefix%
# %player%
# %check_name%
# %description%
# %experimental%
# %vl% - violations
# %verbose% - Informations supplémentaires sur les vérifivations comme les écarts, toutes les vérifications n'ajoutent pas d'information
alerts-format: "%prefix% &f%player% &ba échoué &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
alerts-format-proxy: "%prefix% &f[&cproxy&f] &f%player% &ba échoué &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
experimental-symbol: "*"

profile:
    - "&7======================"
    - "%prefix% &bProfil de &f%player%"
    - "&bPing: &f%ping%"
    - "&bVersion du client : &f%version%"
    - "&bNature du client : &f%brand%"
    - "&bSensibilité Horizontale : &f%h_sensitivity%%"
    - "&bSensibilité Verticale : &f%v_sensitivity%%"
    - "&bFastMath est activé : &f%fast_math%"
    - "&7======================"
help:
    - "&7======================"
    - "/grim alerts &f- &7Activer/Désactiver les alertes"
    - "/grim brands &f- &7Toggle brands"
    - "/grim profile <player> &f- &7Voir le profil du joueur"
    - "/grim help &f- &7Afficher l'aide"
    - "/grim debug <player> &f- &7Afficher les informations de débogage du joueur"
    - "/grim perf &f- &7Afficher les informations de performance"
    - "/grim reload &f- &7Recharger la configuration"
    - "/grim spectate <player> &f- &7Regarder un joueur"
    - "/grim verbose &f- &7Affiche chaqu'une de vos violations, sans tampons"
    - "/grim log [0-255] &f- &7Téléverse un journal de débogage pour les indicateurs de prédiction"
    - "/grim history <player> [page] &f- &7Affiche les alertes précédentes du joueur"
    - "&7======================"

grim-history-load-failure: "%prefix% &cLe sous-système d'historique n'a pas pu être chargé ! Vérifiez la console du serveur pour les erreurs."
grim-history-disabled: "%prefix% &cLe sous‑système d’historique est désactivé!"
# Valid placeholders: %prefix% %player% %page% %maxPages%
grim-history-header: "%prefix% &bAffichage des journaux pour &f%player% (&f%page%&b/&f%maxPages%&f)"
# Valid placeholders: %prefix% %grim_version% %client_brand% %client_version% %server_version% %check% %vl% %verbose% %timeago% %server%
grim-history-entry: "%prefix% &8[&f%server%&8] &bÉchec &f%check% (x&c%vl%&f) &7%verbose% (&bil y a %timeago%&7)"
