# \u00BB é » (>> duplo), ANSI e UTF-8 interpretam isso diferentemente... você pode acabar vendo '?' por conta disso.
prefix: "&bGrim &8\u00BB"

alerts-enabled: "%prefix% &fAlertas habilitados."
alerts-disabled: "%prefix% &fAlertas desabilitados."
verbose-enabled: "%prefix% &fVerbose enabled"
verbose-disabled: "%prefix% &fVerbose disabled"
brands-enabled: "%prefix% &fBrands enabled"
brands-disabled: "%prefix% &fBrands disabled"
client-brand-format: "%prefix% &f%player% entrou usando %brand%."
console-specify-target: "%prefix% &cVocê deve especificar o nome do alvo!"
reloading: "%prefix% &7Reloading config..."
reloaded: "%prefix% &fConfig has been reloaded."
reload-failed: "%prefix% &cFailed to reload config."
player-not-found: "%prefix% &cJogador não encontrado!"
player-not-this-server: "%prefix% &cO jogador não está no servidor!"
spectate-return: "<click:run_command:/grim stopspectating><hover:show_text:\"/grim stopspectating\">\n%prefix% &fClique aqui para retornar a localização anterior\n</hover></click>"
cannot-spectate-return: "%prefix% &cVocê só pode fazer isso após espectar um jogador."
cannot-run-on-self: "%prefix% &cVocê não pode usar esse comando em sí mesmo!"
upload-log: "%prefix% &fRegistro enviado para: %url%"
upload-log-start: "%prefix% &fEnviando o registro. Aguarde."
upload-log-not-found: "%prefix% &cNúmero do registro não encontrado."
upload-log-upload-failure: "%prefix% &cAlgo deu errado ao enviar o registro, veja o terminal para mais informações."
disconnect:
    timeout: "<lang:disconnect.timeout>"
    closed: "<lang:disconnect.closed>"
    error: "<red>An error occurred whilst processing packets. Please contact the administrators."
    blacklisted-forge: "<red>Your forge version is blacklisted due to inbuilt reach hacks.<newline><gold>Versions affected: 1.18.2-1.19.3<newline><newline><red>Please see https://github.com/MinecraftForge/MinecraftForge/issues/9309."
run-as-player: "%prefix% &cEste comando só pode ser usado por jogadores!"
run-as-player-or-console: "%prefix% &cEste comando só pode ser usado por jogadores ou pelo console!"

# Placeholders válidos:
# %prefix%
# %player%
# %check_name%
# %experimental%
# %vl% - violações
# %verbose% - Informação extra da verificação, como os desalinhamentos, nem todas as verifiações terão essa informação.
alerts-format: "%prefix% &f%player% &bfalhou em &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
alerts-format-proxy: "%prefix% &f[&cproxy&f] &f%player% &bfalhou em &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
experimental-symbol: "*"
profile:
    - "&7======================"
    - "%prefix% &bPerfil de &f%player%"
    - "&bPing: &f%ping%"
    - "&bVersão: &f%version%"
    - "&bNome do Cliente: &f%brand%"
    - "&bSensibilidade Horizontal: &f%h_sensitivity%%"
    - "&bSensibilidade Vertical: &f%v_sensitivity%%"
    - "&bFastMath: &f%fast_math%"
    - "&7======================"
help:
    - "&7======================"
    - "/grim alerts &f- &7Alterna noificações."
    - "/grim brands &f- &7Toggle brands"
    - "/grim profile <player> &f- &7Mosta as informações do jogador."
    - "/grim help &f- &7Mostra essa mensagem de ajuda."
    - "/grim debug <player> &f- &7Depura a simulação."
    - "/grim perf &f- &7Depura a performance."
    - "/grim reload &f- &7Recarrega as configurações."
    - "/grim spectate <player> &f- &7Especta um jogador."
    - "/grim verbose &f- &7Esconde as informações extra."
    - "/grim log [0-255] &f- &7Envia o registro da simulação."
    - "/grim history <player> [página] &f- &7Mostra alertas anteriores do jogador"
    - "&7======================"

grim-history-load-failure: "%prefix% &cO subsistema de histórico falhou ao carregar! Verifique o console do servidor por erros."
grim-history-disabled: "%prefix% &cO subsistema de histórico está desativado!"
# Valid placeholders: %prefix% %player% %page% %maxPages%
grim-history-header: "%prefix% &bMostrando logs de &f%player% (&f%page%&b/&f%maxPages%&f)"
# Valid placeholders: %prefix% %grim_version% %client_brand% %client_version% %server_version% %check% %vl% %verbose% %timeago% %server%
grim-history-entry: "%prefix% &8[&f%server%&8] &bFalhou &f%check% (x&c%vl%&f) &7%verbose% (&bhá %timeago%&7)"
