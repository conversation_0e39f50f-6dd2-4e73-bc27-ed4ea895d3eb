# \u00BB is » (double >>), ANSI en UTF-8 interpreteren dit anders... je kunt zelfs "?" zien als gevolg hiervan
prefix: "&bGrim &8\u00BB"

alerts-enabled: "%prefix% &fMeldingen ingeschakeld"
alerts-disabled: "%prefix% &fMeldingen uitgeschakeld"
verbose-enabled: "%prefix% &fVerbose enabled"
verbose-disabled: "%prefix% &fVerbose disabled"
brands-enabled: "%prefix% &fBrands enabled"
brands-disabled: "%prefix% &fBrands disabled"
client-brand-format: "%prefix% &f%player% is lid geworden met %brand%"
console-specify-target: "%prefix% &cJe moet een doel opgeven als de console!"
reloading: "%prefix% &7Reloading config..."
reloaded: "%prefix% &fConfig has been reloaded."
reload-failed: "%prefix% &cFailed to reload config."
player-not-found: "%prefix% &cSpeler is vrijgesteld of offline!"
player-not-this-server: "%prefix% &cSpeler is niet op deze server!"
spectate-return: "<click:run_command:/grim stopspectating><hover:show_text:\"/grim stopspectating\">\n%prefix% &fKlik hier om terug te keren naar de vorige locatie\n</hover></click>"
cannot-spectate-return: "%prefix% &cJe kunt dit alleen doen nadat je een speler hebt bekeken"
cannot-run-on-self: "%prefix% &cJe kunt dit commando niet op jezelf gebruiken!"
upload-log: "%prefix% &fDebug geüpload naar: %url%"
upload-log-start: "%prefix% &fLog wordt geüpload... even geduld"
upload-log-not-found: "%prefix% &cKan dat log niet vinden"
upload-log-upload-failure: "%prefix% &cEr is iets misgegaan tijdens het uploaden van dit log, zie console voor meer informatie"
disconnect:
    timeout: "<lang:disconnect.timeout>"
    closed: "<lang:disconnect.closed>"
    error: "<red>An error occurred whilst processing packets. Please contact the administrators."
    blacklisted-forge: "<red>Your forge version is blacklisted due to inbuilt reach hacks.<newline><gold>Versions affected: 1.18.2-1.19.3<newline><newline><red>Please see https://github.com/MinecraftForge/MinecraftForge/issues/9309."
run-as-player: "%prefix% &cDit commando kan alleen door spelers worden gebruikt!"
run-as-player-or-console: "%prefix% &cDit commando kan alleen door spelers of de console worden gebruikt!"

# Geldige plaatsaanduidingen:
# %prefix%
# %player%
# %check_name%
# %description%
# %experimental%
# %vl% - overtredingen
# %verbose% - extra informatie van de controle zoals offsets, niet alle controles voegen informatie toe
alerts-format: "%prefix% &f%player% &bmislukt &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
alerts-format-proxy: "%prefix% &f[&cproxy&f] &f%player% &bmislukt &f%check_name%%experimental% &f(x&c%vl%&f) &7%verbose%"
experimental-symbol: "*"

profile:
    - "&7======================"
    - "%prefix% &bProfiel voor &f%player%"
    - "&bPing: &f%ping%"
    - "&bVersie: &f%version%"
    - "&bClient Merk: &f%brand%"
    - "&bHorizontale Gevoeligheid: &f%h_sensitivity%%"
    - "&bVerticale Gevoeligheid: &f%v_sensitivity%%"
    - "&bFastMath Ingeschakeld: &f%fast_math%"
    - "&7======================"
help:
    - "&7======================"
    - "/grim alerts &f- &7Meldingen inschakelen"
    - "/grim brands &f- &7Toggle brands"
    - "/grim profile <player> &f- &7Bekijk speler info"
    - "/grim help &f- &7Bekijk dit helpbericht"
    - "/grim debug <player> &f- &7Voorspellingsuitvoer ontwikkelaar"
    - "/grim perf &f- &7Ontwikkelaar ms/voorspelling"
    - "/grim reload &f- &7Laadt de configuratie opnieuw"
    - "/grim spectate <player> &f- &7Een speler bekijken"
    - "/grim verbose &f- &7Toont elke flag, zonder buffers"
    - "/grim log [0-255] &f- &7Uploadt een debug-log voor voorspellings-flaggen"
    - "/grim history <player> [pagina] &f- &7Toont eerdere waarschuwingen voor de speler"
    - "&7======================"

grim-history-load-failure: "%prefix% &cGeschiedenissubsysteem kon niet worden geladen! Controleer de serverconsole op fouten."
grim-history-disabled: "%prefix% &cHet geschiedenissubsysteem is uitgeschakeld!"
# Valid placeholders: %prefix% %player% %page% %maxPages%
grim-history-header: "%prefix% &bLogbestanden tonen voor &f%player% (&f%page%&b/&f%maxPages%&f)"
# Valid placeholders: %prefix% %grim_version% %client_brand% %client_version% %server_version% %check% %vl% %verbose% %timeago% %server%
grim-history-entry: "%prefix% &8[&f%server%&8] &bMislukt &f%check% (x&c%vl%&f) &7%verbose% (&b%timeago% geleden&7)"
