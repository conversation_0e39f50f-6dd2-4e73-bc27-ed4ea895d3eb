# Configuração padrão do GrimAC
# Código fonte: https://github.com/MWHunter/Grim
# Copyright 2025 DefineOutside e contribuidores, Sob licença GPLv3.
# Códigos modificados, ou plugins que utilizam o código do Grim, devem ser privados,
# ou seu código deve estar totalmente disponível para compradores sem quaisquer custos adicionais.

alerts:
    # Além de transmitir alertas para jogadores, eles deveriam ser transmitidos para o terminal também?
    print-to-console: true
    # Isso controla se/como os alertas são compartilhados entre os servidores conectados ao proxy.
    # Deve-se ter 'bungee-plugin-message-channel' habilitado na configuração do Velocity se estiver usando.
    proxy:
        # Deviam os alertas ser enviados para outros servidores conectados no mesmo proxy?
        send: false
        # Deviam os alertas ser recebidos de outros servidores conectados no mesmo proxy?
        receive: false

verbose:
    print-to-console: false

check-for-updates: true

client-brand:
    # Isso signifia que, caso a marca do cliente seja igual às listadas abaixo, não será notificado aos
    # administradores.
    ignored-clients:
        - "^vanilla$"
        - "^fabric$"
        - "^lunarclient:v\\d+\\.\\d+\\.\\d+-\\d{4}$"
        - "^Feather Fabric$"
        - "^labymod$"
    # Grim will blacklist specific Forge versions which include built-in Reach hacks (Forge 1.18.2 to 1.19.3).
    # Setting this option to false will allow said clients to connect to the server. Disable this at your own risk.
    disconnect-blacklisted-forge-versions: true

spectators:
    # Esconde todos espectadores com a permissão 'grim.spectator' mesmo se não estiverem ativamente espectando.
    hide-regardless: false
    # Fará os espectadores serem escondidos apenas nos mundos listados abaixo, deixe em branco para habilitar em todos os mundos.
    allowed-worlds:
        - ""

# Quanto tempo os jogadores tem até expulsarmos eles por inatividade? Padrão = 60 segundos.
max-transaction-time: 60

# Enable this to stop Grim cancelling pongs received from players.
# This may improve compatibility with other anticheats. It may cause issues with packet limiters.
# Do not enable this if you are not sure what you are doing.
disable-pong-cancelling: false

# Deve-se cancelar o pacote de movimento duplicado?
# A Mojang arrumou esse erro na 1.21, sendo sua tentativa de concertar a dessincronização do balde. https://bugs.mojang.com/browse/MC-12363
# Isso só se aplica à clientes da 1.17-1.20.5 em servidores da 1.8.
cancel-duplicate-packet: true

# Deve-se ignorar rotação em pacotes duplicados?
ignore-duplicate-packet-rotation: false

Simulation:
    # Por quanto deve-se multiplicar a vantagem desde a última vez que falhou uma verificação?
    # Isso é como a configuração padrão se parece (eixo X = segundos, eixo Y = 1/1000 blocos): https://www.desmos.com/calculator/d4ufgxrxer
    setback-decay-multiplier: 0.999
    # O quão longe o jogador deve estar da área simulada para registrar violações?
    # Medido em blocos do último movimento legítmo.
    # Incluímos o Optifine trocando as tabelas trigonométricas; entretanto, abaixar para 0.001 reduzirá o 'FastMath'
    # de falhar, caso essa compensação não funcionar.
    threshold: 0.001
    # O quão longe o jogador deve estar da área simulada para imediatamente recuá-lo?
    # -1 desabilitará
    immediate-setback-threshold: 0.1
    # Qual é a soma do desalinhamento da simulação em todos os ticks até começarmos a recuar?
    # -1 desabilitará
    max-advantage: 1
    # Depois de 50 segundos, com a configuração padrão, o jogador irá de 4 blocos a 1 bloco de vantagem.
    # Isso serve para previnir o jogador de alcançar violações demais e nunca conseguir se livrar delas.
    # Cela de vantagens padrão (eixo X = segundos, eixo Y = 1/1000 blocos): https://www.desmos.com/calculator/4lovswdarj
    max-ceiling: 4
    # Quantas violações para começar a recuar o jogador?
    # 1 para comportamento antigo
    setback-violation-threshold: 1

# Verifica se um jogador entrou em um bloco ao se mover.
Phase:
    setbackvl: 1 # Entrar em blocos possibilita escalar paredes, além disso, essa verificação é relativamente estável.
    decay: 0.005

AirLiquidPlace:
    cancelvl: 0

FabricatedPlace:
    cancelvl: 5

FarPlace:
    cancelvl: 5

PositionPlace:
    cancelvl: 5

RotationPlace:
    cancelvl: 5

# Verificação de NoSlow por simulação.
# Grim considera erros no netcode. Difícil de gerar falsos positivos, até mesmo spamando o blotão direito e a offhand.
# Muito mais estável que outros anticheats, mas por favor, reporte quaisquer falsos positivos. Eu já arrumei diversos
# erros de netcode.
NoSlow:
    # Quanto de desalinhamento é considerado trapaça?
    # Por padrão é menor que os outros.
    # Em 0.03-0.2 detecta com consistência.
    threshold: 0.001
    # Recuo imediato para inibir as vantagens rapidamente.
    setbackvl: 5
    # Decai quando o jogador usa um item e é efetivamente desacelerado.
    decay: 0.05

Knockback:
    # Por quanto deve-se multiplicar a vantagem desde a última vez que falhou na verificação?
    setback-decay-multiplier: 0.999
    # O quão longe da área do recuo recebido o jogador deve estar para registrar violações?
    # Medido em blocos do recuo recebido.
    threshold: 0.001
    # O quão longe o jogador deve estar da área simulada para imediatamente recuá-lo?
    # -1 desabilitará.
    immediate-setback-threshold: 0.1
    # Qual é a soma do desalinhamento da simulação em todos os ticks até começarmos a recuar?
    # -1 desabilitará.
    max-advantage: 1
    # Isso serve para previnir o jogador de alcançar violações demais e nunca conseguir se livrar delas.
    max-ceiling: 4

Explosion:
    threshold: 0.001
    setbackvl: 3

TimerA:
    setbackvl: 10
    # Milissegundos que o jogador pode acumular para depois usar quando eles têm um atraso no ping.
    # Pode ignorar o uso de fast use / fast heal / fast bow na 1.8 caso o valor seja definido muito alto.
    # 120ms parece um bom balanceamento.
    drift: 120

# This check limits abuse of the TimerA balance by preventing the player's movement falling too far behind realtime
TimerLimit:
    # Ping no qual a verificação vai começar a limitar o balançeamento do timer para previnir abuso.
    # Pode causar recuos a jogadores legítmos, mas somente se estiverem acima desse ping.
    # -1 desabilitará.
    ping-abuse-limit-threshold: 1000

NegativeTimer:
    # Número de milissegundos perdidos ao se mover antes de começarmos a registrar violações.
    drift: 1200

# Mesmo método usado no TimerA, mas para veículos.
VehicleTimer:
    # Detecta até 1.005 de timer.
    setbackvl: 10

PacketOrderI:
    # Ativar se os jogadores estão sendo sinalizados por usar mods de animação 1.7
    exempt-placing-while-digging: false

Reach:
    # Por quanto deve-se expandir as caixas de colisão? 0.0005 deve detectar 3.0005+ de alcance.
    #
    # Há 0.03 de expansão forçada nas versões 1.9-1.18.1 (não a 1.18.2), ou algumas combinações de cliente/servidor por
    # conta de mudanças no protocolo e limitações. Essa verificação é mais poderosa com clientes da 1.7/1.8 em servidores
    # da 1.8.
    threshold: 0.0005
    # Deve-se cancelar hits que sabemos que são impossíveis?
    # O alcance de 3.00-3.03 pode acabar passando, por conta da ordem de pacotes, mas continuará registrando violações.
    block-impossible-hits: true
    # Isso enviará um pacote adicional no final de todos os ticks para aumentar a probabilidade de detectarmos trapaceiros.
    # Isso injeta na lista de conecção do servidor para envar um pacote final logo antes do servidor recarregar.
    # Habilitando isso, irá aumentar o uso de banda para todos os jogadores.
    # Isso não irá interferir na performance do servidor.
    # Habilitando isso, aumentará a probabilidade de detectarmos trapaceiros.
    # Deixando-o desabilitado, continuará detectando trapaceiros e não irá causar falsos positivos.
    # A menos que estiver em um servidor focado em PvP, esse pacote adicional não é recomendado.
    enable-post-packet: false

exploit:
    # Você pode ganhar altas velocidades se pular correndo com uma elytra, previne isso de ocorrer quando definido falso.
    # A Mojang vacilou com o netcode por fazer a Elytra iniciar pelo lado do cliente.
    # As Elytras levam 0.99 de fricção horizontal, então constantemente adicionando 0.2 de velocidade horizontal resulta
    # em velocidades altíssimas.
    allow-sprint-jumping-when-using-elytra: true
    # Essa opção mitiga posicionar blocos em blocos fantasma, ressincronizando o jogador quando isso ocorrer.
    allow-building-on-ghostblocks: true
    # Esse ajuste, influído pela opção acima, define a distância para verificar blocos fantasmas.
    # O valor válido é limitado de 2 a 4.
    distance-to-check-if-ghostblocks: 2

# Registra plugins que são injetados na netty ao entrar para depurar erros de compatibilidade.
debug-pipeline-on-join: false

# Habilita verificações experimentais.
experimental-checks: false

reset-item-usage-on-item-update: true
reset-item-usage-on-attack: true
reset-item-usage-on-slot-change: true

# Grim às vezes cancela pacotes ilegais como com o timer, depois de X pacotes em um segundo que foram cancelados, quando
# deve-se simplesmente expulsar o jogador? Isso é requirido já que alguns limitadores de pacotes não contam pacotes
# cancelados pelo Grim.
packet-spam-threshold: 100
# Habilite isto para imprimir um stacktrace quando um jogador for expulso devido ao packet-spam-threshold
debug-packet-cancel: false
# Grim pode impor que o jogador fora do estado de voo não possa ter mais de X milissegundos de ping.
# Isso ocorre porque o Grim atualmente não verifica os jogadores que estão voando.
# -1 desabilitará.
max-ping-out-of-flying: 1000

# Ping máximo quando o impulso do foguete acaba.
# Previne jogadores com ping alto de usarem um foguete ara voar indefinidamente.
max-ping-firework-boost: 1000

history:
    enabled: true
    # Quantas entradas devem ser mostradas por página com /grim history <player>
    entries-per-page: 15
    # Qual nome de servidor deve ser inserido no comando history? Útil se você usa o mesmo banco de dados para vários servidores
    server-name: Prison
    database:
        # Use SQLITE para armazenamento local ou MYSQL para um banco MySQL externo. Atualizado somente ao reiniciar o servidor
        type: SQLITE
        # Detalhes de conexão MySQL
        host: localhost
        port: 3306
        database: grim
        username: root
        password: ""

config-version: 9
